# 严格批判性审查检查清单

## 使用说明

本检查清单专门用于严格批判性审查，目的是发现和指出专利文件中的所有问题、缺陷和错误。

**核心原则**:
- 绝对不说优点，只指出问题
- 用最严厉的语言批评缺陷
- 毫不留情地指出所有错误
- 专门识别AI生成内容的典型问题

---

## 第一级：AI生成内容问题严厉批判

### AI典型问题识别 (必须全部检查)

- [ ] **不确定表达问题**: 是否使用了"能够"、"可以"、"有助于"等不确定表达？
  - 发现问题时批判语言: "这种不确定表达完全不符合专利撰写的确定性要求"

- [ ] **评价性词语问题**: 是否包含"优势"、"效果"、"影响"、"好处"等评价性词语？
  - 发现问题时批判语言: "专利文件不是广告，不应包含这些营销式评价词语"

- [ ] **解释性语言问题**: 是否出现"这样做的目的是..."、"为了..."等解释性表达？
  - 发现问题时批判语言: "专利文件应直接描述技术方案，不需要解释性语言"

- [ ] **技术描述宽泛问题**: 技术描述是否过于宽泛，缺乏具体参数？
  - 发现问题时批判语言: "技术描述过于宽泛，缺乏可验证的具体参数"

- [ ] **主观评价问题**: 是否包含对技术方案的主观评价？
  - 发现问题时批判语言: "专利文件必须客观描述，不能包含主观评价"

**AI问题批判评分**: 发现问题数量 _____ 个 (每发现一个问题都必须严厉批评)

---

## 第二级：技术方案严格批判

### 技术完整性严厉检查

- [ ] **技术方案逻辑问题**: 技术方案是否存在逻辑漏洞或不完整？
  - 发现问题时批判语言: "技术方案逻辑存在严重缺陷，无法构成完整的技术解决方案"

- [ ] **必要技术特征缺失**: 是否缺乏实现发明目的的必要技术特征？
  - 发现问题时批判语言: "缺乏关键技术特征，技术方案不完整"

- [ ] **实施方式不充分**: 实施方式描述是否不充分或不清晰？
  - 发现问题时批判语言: "实施方式描述严重不足，无法指导本领域技术人员实施"

- [ ] **技术参数缺失**: 是否缺乏必要的技术参数和限定条件？
  - 发现问题时批判语言: "缺乏具体技术参数，技术方案过于抽象"

- [ ] **创新点不明确**: 技术创新点是否表达不明确？
  - 发现问题时批判语言: "创新点表达模糊，无法体现技术贡献"

**技术方案批判评分**: 发现问题数量 _____ 个

---

## 第三级：法律合规性严厉批判

### 权利要求书结构严格检查

- [ ] **独立权利要求问题**: 独立权利要求是否缺乏必要技术特征？
  - 发现问题时批判语言: "独立权利要求结构严重不合理，缺乏必要技术特征"

- [ ] **从属权利要求问题**: 从属权利要求引用关系是否错误？
  - 发现问题时批判语言: "从属权利要求引用关系混乱，逻辑错误"

- [ ] **保护范围问题**: 保护范围设计是否过宽或过窄？
  - 发现问题时批判语言: "保护范围设计不合理，存在重大缺陷"

- [ ] **权利要求一致性**: 权利要求与说明书内容是否不一致？
  - 发现问题时批判语言: "权利要求与说明书严重不一致，违反法律要求"

**法律合规批判评分**: 发现问题数量 _____ 个

---

## 第四级：语言表达严厉批判

### 专利撰写语言严格检查

- [ ] **"所述"使用错误**: "所述"使用是否错误或不当？
  - 发现问题时批判语言: "所述使用完全错误，不符合专利撰写基本规范"

- [ ] **句式结构问题**: 句式是否不符合专利撰写标准？
  - 发现问题时批判语言: "句式结构严重不规范，不符合专利撰写要求"

- [ ] **术语使用问题**: 术语使用是否不一致或不准确？
  - 发现问题时批判语言: "术语使用混乱，严重影响技术方案的准确性"

- [ ] **语法错误**: 是否存在语法错误和表达模糊？
  - 发现问题时批判语言: "语法错误严重，表达极不规范"

- [ ] **逻辑连接问题**: 逻辑连接词使用是否不当？
  - 发现问题时批判语言: "逻辑连接词使用错误，影响技术方案的逻辑性"

**语言表达批判评分**: 发现问题数量 _____ 个

---

## 第五级：格式规范严厉批判

### 格式标准严格检查

- [ ] **编号错误**: 权利要求编号是否连续且正确？
  - 发现问题时批判语言: "编号系统混乱，格式极不规范"

- [ ] **标点符号问题**: 标点符号使用是否规范？
  - 发现问题时批判语言: "标点符号使用错误，不符合专利文件格式要求"

- [ ] **段落结构问题**: 段落结构是否清晰？
  - 发现问题时批判语言: "段落结构混乱，严重影响文件可读性"

**格式规范批判评分**: 发现问题数量 _____ 个

---

## 严厉批判总结

### 问题统计
- AI生成问题: _____ 个
- 技术方案问题: _____ 个  
- 法律合规问题: _____ 个
- 语言表达问题: _____ 个
- 格式规范问题: _____ 个

**总问题数量**: _____ 个

### 严厉批判结论

**如果发现问题 > 10个**:
"这份专利文件存在严重问题，质量极差，必须进行大幅修改才能达到基本要求。"

**如果发现问题 5-10个**:
"这份专利文件存在较多问题，质量不合格，需要全面修改。"

**如果发现问题 1-4个**:
"这份专利文件仍存在问题，必须修改后才能提交。"

**如果发现问题 = 0个**:
"经过严格审查，暂未发现明显问题，但仍需保持警惕。"

### 必须立即修改的严重错误清单

1. [列出所有发现的问题]
2. [每个问题都要用严厉语言批评]
3. [提供具体的修改要求]

### 严格改进要求

- [ ] 立即删除所有AI生成内容的典型问题
- [ ] 重新完善技术方案的逻辑结构
- [ ] 修正所有法律合规性问题
- [ ] 规范所有语言表达错误
- [ ] 统一所有格式规范问题

**检查完成时间**: [日期]
**严格批判审查员**: 严教授
**要求复查时间**: 修改完成后立即重新审查
