# 专门严格批判检查清单 - AI内容问题专项批判

## 使用说明

**本检查清单的唯一目的：发现问题、批判缺陷、拒绝妥协**

### 核心批判原则
- ❌ **绝对禁止**说任何优点或正面评价
- ❌ **绝对禁止**使用"还不错"、"基本可以"等模糊表达
- ❌ **绝对禁止**给出任何鼓励性语言
- ✅ **必须**用最严厉的语言指出每一个问题
- ✅ **必须**毫不留情地批评所有缺陷
- ✅ **必须**专门针对AI生成内容的典型问题进行深度批判

---

## 第一级：AI生成内容典型问题深度批判

### A. 不确定性表达严厉检查

**检查目标**: 发现所有模糊、不确定的表达

- [ ] **"能够"问题**: 是否使用"能够"、"可以"、"可能"？
  - **严厉批判**: "这种不确定表达完全违反专利撰写的确定性要求，必须立即删除！"

- [ ] **"有助于"问题**: 是否使用"有助于"、"有利于"、"便于"？
  - **严厉批判**: "这种模糊表达毫无意义，专利文件不是建议书！"

- [ ] **"一定程度上"问题**: 是否使用"一定程度上"、"在某种程度上"？
  - **严厉批判**: "这种含糊表达完全不可接受，技术方案必须明确！"

- [ ] **"通常"、"一般"问题**: 是否使用"通常"、"一般"、"往往"？
  - **严厉批判**: "这种概率性表达严重违反专利撰写规范！"

**不确定性问题计数**: _____ 个 (每个都是严重错误)

### B. 评价性语言严厉检查

**检查目标**: 消除所有主观评价和营销语言

- [ ] **优势词汇**: 是否包含"优势"、"优点"、"好处"、"益处"？
  - **严厉批判**: "专利文件不是产品宣传册，这些营销词汇必须全部删除！"

- [ ] **效果词汇**: 是否包含"显著效果"、"良好效果"、"理想效果"？
  - **严厉批判**: "效果评价属于主观判断，严重违反专利客观性要求！"

- [ ] **性能词汇**: 是否包含"高性能"、"优异性能"、"卓越性能"？
  - **严厉批判**: "性能评价缺乏客观标准，这种表达完全不合格！"

- [ ] **质量词汇**: 是否包含"高质量"、"优质"、"精确"？
  - **严厉批判**: "质量评价过于主观，必须用具体参数替代！"

**评价性语言问题计数**: _____ 个 (每个都违反客观性原则)

### C. 解释性语言严厉检查

**检查目标**: 删除所有不必要的解释和说明

- [ ] **目的解释**: 是否出现"这样做的目的是..."、"为了..."？
  - **严厉批判**: "专利文件应直接描述技术方案，不需要解释目的！"

- [ ] **原因解释**: 是否出现"这是因为..."、"由于..."？
  - **严厉批判**: "原因解释属于多余内容，技术方案应自明！"

- [ ] **背景解释**: 是否过度解释技术背景？
  - **严厉批判**: "背景解释过度冗余，严重偏离专利撰写重点！"

- [ ] **工作原理解释**: 是否过度解释工作原理？
  - **严厉批判**: "原理解释应简洁明确，不应冗长啰嗦！"

**解释性语言问题计数**: _____ 个 (每个都是冗余内容)

### D. 技术描述宽泛性严厉检查

**检查目标**: 发现所有过于宽泛、缺乏具体性的技术描述

- [ ] **参数缺失**: 技术方案是否缺乏具体数值参数？
  - **严厉批判**: "缺乏具体参数的技术描述毫无价值，无法验证！"

- [ ] **范围模糊**: 技术特征范围是否过于宽泛？
  - **严厉批判**: "技术范围过于宽泛，保护范围不明确！"

- [ ] **条件不明**: 实施条件是否描述不清？
  - **严厉批判**: "实施条件模糊不清，无法指导实际操作！"

- [ ] **步骤抽象**: 技术步骤是否过于抽象？
  - **严厉批判**: "技术步骤过于抽象，缺乏可操作性！"

**技术描述问题计数**: _____ 个 (每个都影响技术方案的可实施性)

---

## 第二级：AI生成内容深层问题批判

### E. 逻辑结构严厉检查

- [ ] **逻辑跳跃**: 技术方案是否存在逻辑跳跃？
  - **严厉批判**: "逻辑跳跃严重，技术方案不连贯！"

- [ ] **因果关系错误**: 因果关系描述是否错误？
  - **严厉批判**: "因果关系描述错误，逻辑严重混乱！"

- [ ] **层次混乱**: 技术层次是否混乱？
  - **严厉批判**: "技术层次混乱，结构极不合理！"

**逻辑问题计数**: _____ 个

### F. 术语使用严厉检查

- [ ] **术语不一致**: 同一概念是否使用不同术语？
  - **严厉批判**: "术语使用不一致，严重影响技术方案的准确性！"

- [ ] **术语不准确**: 术语使用是否不准确？
  - **严厉批判**: "术语使用不准确，技术表达错误！"

- [ ] **自造术语**: 是否使用非标准术语？
  - **严厉批判**: "自造术语不可接受，必须使用标准术语！"

**术语问题计数**: _____ 个

---

## 第三级：专利撰写规范严厉检查

### G. "所述"使用严厉检查

- [ ] **"所述"滥用**: 是否过度使用"所述"？
  - **严厉批判**: "所述使用过度，严重影响文件可读性！"

- [ ] **"所述"误用**: "所述"使用是否错误？
  - **严厉批判**: "所述使用错误，不符合专利撰写基本规范！"

- [ ] **"所述"缺失**: 应使用"所述"处是否缺失？
  - **严厉批判**: "所述缺失，引用关系不明确！"

**"所述"问题计数**: _____ 个

### G2. 描述性标题严厉批判

- [ ] **"对...进行..."格式**: 是否使用"对...进行..."等描述性标题？
  - **严厉批判**: "描述性标题完全违反专利撰写规范！技术方案必须直接陈述！"

- [ ] **非直接描述**: 是否使用标题+描述的学术论文格式？
  - **严厉批判**: "这不是学术论文！专利文件必须直接描述技术方案！"

- [ ] **算法步骤标题化**: 算法步骤是否用标题形式而非直接陈述？
  - **严厉批判**: "算法步骤必须直接以条件或动作开始，不能用标题格式！"

**描述性标题问题计数**: _____ 个

### G3. 专利写作模式严厉批判

- [ ] **步骤格式错误**: 方法步骤是否未按"步骤X、[动作]"格式撰写？
  - **严厉批判**: "步骤格式完全错误！必须严格按照'步骤X、[动作描述]'格式！"

- [ ] **缺少总体技术方案**: 步骤描述是否缺少总体技术方案说明？
  - **严厉批判**: "步骤描述不完整！必须包含总体技术方案和具体实现条件！"

- [ ] **缺少具体实现条件**: 是否缺少"其中..."的具体实现条件？
  - **严厉批判**: "技术方案过于抽象！必须包含具体的实现条件和参数！"

- [ ] **层次结构混乱**: 是否未遵循"做什么→怎么做→具体条件"的三层结构？
  - **严厉批判**: "层次结构完全混乱！必须严格按照三层递进结构撰写！"

**写作模式问题计数**: _____ 个

### G4. 技术符号一致性严厉批判

- [ ] **符号定义缺失**: 新符号首次出现时是否未立即定义？
  - **严厉批判**: "符号定义缺失！每个技术符号首次出现时必须立即定义！"

- [ ] **符号使用不一致**: 同一概念是否使用了不同符号？
  - **严厉批判**: "符号使用极不规范！同一概念必须使用统一符号！"

- [ ] **总体与具体不一致**: 总体描述与具体方案中的符号是否不一致？
  - **严厉批判**: "符号一致性严重违规！总体描述的符号必须在具体方案中保持一致！"

- [ ] **公式符号错误**: 公式中的符号是否与文字描述不符？
  - **严厉批判**: "公式符号错误！公式中的每个符号都必须与文字描述完全一致！"

**符号一致性问题计数**: _____ 个

### H. 权利要求结构严厉检查

- [ ] **独立权利要求缺陷**: 独立权利要求是否存在结构缺陷？
  - **严厉批判**: "独立权利要求结构严重缺陷，保护范围不合理！"

- [ ] **从属权利要求错误**: 从属权利要求引用是否错误？
  - **严厉批判**: "从属权利要求引用错误，逻辑关系混乱！"

- [ ] **权利要求冗余**: 是否存在冗余的权利要求？
  - **严厉批判**: "权利要求冗余，保护策略不合理！"

**权利要求问题计数**: _____ 个

---

## 严厉批判总结评估

### 问题统计表
| 问题类别 | 发现数量 | 严重程度 |
|---------|---------|---------|
| 不确定性表达 | _____ | 严重 |
| 评价性语言 | _____ | 严重 |
| 解释性语言 | _____ | 中等 |
| 技术描述宽泛 | _____ | 严重 |
| 逻辑结构 | _____ | 严重 |
| 术语使用 | _____ | 中等 |
| "所述"使用 | _____ | 中等 |
| 描述性标题 | _____ | 严重 |
| 写作模式违规 | _____ | 严重 |
| 符号一致性 | _____ | 严重 |
| 权利要求结构 | _____ | 严重 |

**总问题数量**: _____ 个

### 严厉批判结论

**如果总问题 ≥ 15个**:
"这份专利文件完全不合格！存在大量严重问题，质量极差，必须重新撰写！"

**如果总问题 10-14个**:
"这份专利文件问题严重，质量不达标，需要大幅修改！"

**如果总问题 5-9个**:
"这份专利文件存在较多问题，必须全面修改才能达到基本要求！"

**如果总问题 1-4个**:
"这份专利文件仍有问题，必须修改后才能提交！"

**如果总问题 = 0个**:
"经过严格批判审查，暂未发现明显问题，但仍需保持高度警惕！"

### 立即修改要求清单

**必须立即删除的内容**:
1. [列出所有不确定性表达]
2. [列出所有评价性语言]
3. [列出所有解释性语言]

**必须立即修正的错误**:
1. [列出所有技术描述问题]
2. [列出所有逻辑结构问题]
3. [列出所有术语使用问题]

**必须立即规范的格式**:
1. [列出所有"所述"使用问题]
2. [列出所有权利要求问题]

### 严格改进指令

- [ ] **第一步**: 删除所有AI生成内容的典型问题表达
- [ ] **第二步**: 重新撰写所有技术描述，增加具体参数
- [ ] **第三步**: 修正所有逻辑结构和术语使用问题
- [ ] **第四步**: 规范所有专利撰写格式
- [ ] **第五步**: 重新审查修改后的内容

**严格批判审查完成时间**: [日期]
**严格批判审查员**: 严教授
**要求重新提交时间**: 修改完成后24小时内
**警告**: 如不按要求修改，将拒绝接受此专利文件！
