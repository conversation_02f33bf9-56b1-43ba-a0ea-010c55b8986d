# 专利撰写标准和规范

## 基于实际案例的撰写标准

本文档基于提供的专利案例（CN111388003B、CN117159019B、CN118349676A）和用户自定义的撰写指令，总结专利撰写的标准和规范。

## 一、通用撰写原则

### 1.1 核心要求
- **清晰、精确、无歧义**: 每个句式和描述都必须明确定义组件的功能或步骤的操作
- **指代明确**: 使用"所述"指代前文中已提及的任何实体
- **避免冗余与层次分明**: 独立权利要求包含必要技术特征，从属权利要求包含具体实现细节

### 1.2 语言规范
- 专利文本是法律文件，必须高度严谨
- 确保技术方案的可验证性和可复现性
- 避免使用模糊、宽泛或可能产生歧义的措辞
- **直接描述原则**: 技术方案直接陈述，避免描述性标题开头
- **避免"对...进行..."格式**: 不使用"对连续的肠鸣音帧进行事件合并"等表述
- **直接条件陈述**: 使用"当...时"、"将..."等直接表述方式

## 二、系统权利要求撰写标准

### 2.1 核心功能描述句式："用于..."

**标准句式结构**: "所述[组件名称]用于[动词短语描述其功能目的]"

**实际案例示例**:
```
信号采集单元，用于采集被检测对象的多个第一声音信号
信号处理单元，电连接于所述信号采集单元，用于对所述多个第一声音信号进行处理
信号传输单元，电连接于所述信号处理单元，用于传输所述多个第二声音信号至所述体音确定装置
```

### 2.2 组件构成描述
**标准句式**: "所述[组件名称]包括[子组件列表]"

**实际案例示例**:
```
所述信号采集单元包括M个拾音传感器
所述柔性封装单元从上至下依次包括第一封装层、第二封装层、第三封装层、第四封装层
```

### 2.3 技术参数和限定条件
**标准格式**: 在描述中明确技术参数和限定条件

**实际案例示例**:
```
M为大于或等于3的奇数
所述柔性电路板的厚度≤0.5mm，最小弯曲半径≥2mm
所述听筒的长度为1.8mm～2.5mm
```

## 三、方法权利要求撰写标准

### 3.1 开篇句式
**标准格式**: "一种...方法，其特征在于，按如下步骤进行："

### 3.2 步骤描述规范
**强动词开头**: 每个步骤都应以强有力的动词开头
- 常用动词: 构建、获取、利用、获得、采集、设定、比较、判断、输入、处理、输出、计算、训练、更新、划分、映射

**输入-操作-输出模式**: 清晰指出步骤的输入、执行的操作和产生的输出

**直接描述原则**:
- ✅ 正确: "当相邻帧间隔小于预设时间阈值时，将其合并为同一事件"
- ❌ 错误: "对连续的肠鸣音帧进行事件合并：当相邻帧间隔小于..."
- 算法步骤直接以条件、动作或结果开始，避免描述性标题

### 3.3 结果连接："从而..."
**标准句式**: 用于连接前后操作，表明直接导致的结果或目的

**实际案例示例**:
```
利用所述多个第二声音信号确定多个待检测信号；
将每个待检测信号进行加窗分帧处理，并对得到的各个帧进行短时FFT变换，得到待检测信号的互功率谱函数；
对所述互功率谱函数进行处理，得到时延估计值；根据最小二乘法及所述时延估计值对被检测对象的声源位置进行定位，以确定所述待检测信号在被测对象的发声位置
```

### 3.4 条件逻辑表达："若...则..."
**标准句式**: 用于清晰展现方法中的条件判断和后续操作

**实际案例示例**:
```
根据所述用户的身体恢复阶段确定激活贴合于所述用户的腹部的不同区域的所述检测单元的位置和/或时长
当所述用户的身体恢复阶段处于恢复初期时，激活贴合于所述用户的腹部的不同区域的所有检测单元
```

## 四、从属权利要求撰写标准

### 4.1 引导句式
**标准格式**: "根据权利要求X所述的一种...方法/系统，其特征在于，所述[组件名称/步骤名称]包括："

**实际案例示例**:
```
根据权利要求1所述的装置，其特征在于，M个拾音传感器设置于所述柔性电路板被所述第二封装层覆盖的一侧的中心线上
根据权利要求2所述的装置，其特征在于，所述拾音传感器包括MEMS麦克风、驻极体麦克风、压电传感器
```

### 4.2 详细拆解与编号
- 对主权利要求中的某一组件进行更具体的拆解和详细描述
- 可以引入具体的算法、数据结构、组件的实现方式、条件判断等
- 使用清晰的层次结构组织内容

## 五、电子设备/计算机可读介质权利要求

### 5.1 电子设备权利要求标准格式
```
一种电子设备，包括存储器以及处理器，其特征在于，所述存储器用于存储支持处理器执行权利要求[范围]中任一所述[方法名称]的程序，所述处理器被配置为用于执行所述存储器中存储的程序。
```

### 5.2 计算机可读存储介质权利要求标准格式
```
一种计算机可读存储介质，计算机可读存储介质上存储有计算机程序，其特征在于，所述计算机程序被处理器运行时执行权利要求[范围]中任一所述[方法名称]的步骤。
```

## 六、质量控制要点

### 6.1 语言规范检查
- [ ] 正确使用"所述"指代
- [ ] 功能描述使用标准"用于..."句式
- [ ] 方法步骤以强动词开头
- [ ] 避免模糊、宽泛的措辞

### 6.2 逻辑结构检查
- [ ] 独立权利要求包含必要技术特征
- [ ] 从属权利要求合理限定主权利要求
- [ ] 权利要求间引用关系正确
- [ ] 技术方案具有可实施性

### 6.3 技术准确性检查
- [ ] 技术术语使用准确一致
- [ ] 技术参数和数值准确
- [ ] 技术方案逻辑合理
- [ ] 与现有技术区别明确

## 七、常见错误及避免方法

### 7.1 语言表达错误
- **错误**: 使用"该"、"这个"等模糊指代
- **正确**: 使用"所述"进行明确指代

### 7.2 功能描述错误
- **错误**: "信号采集单元采集信号"
- **正确**: "信号采集单元，用于采集被检测对象的多个第一声音信号"

### 7.3 逻辑关系错误
- **错误**: 步骤间缺乏逻辑连接
- **正确**: 使用"从而"、"若...则..."明确逻辑关系

### 7.4 技术特征遗漏
- **错误**: 独立权利要求缺少关键技术特征
- **正确**: 确保包含实现发明目的的所有必要技术特征

## 八、最佳实践建议

### 8.1 撰写前准备
1. 深入理解技术方案的核心创新点
2. 分析现有技术的不足和本发明的优势
3. 确定保护范围和撰写策略

### 8.2 撰写过程控制
1. 严格按照标准句式撰写
2. 保持技术术语的一致性
3. 确保逻辑关系清晰明确

### 8.3 撰写后检查
1. 使用专利质量检查清单验证
2. 进行多轮审查和修改
3. 确保与说明书内容一致
