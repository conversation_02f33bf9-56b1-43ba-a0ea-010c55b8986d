name: patent-writing
version: 1.0.0
description: >-
  专业的专利申请文件撰写和质量控制系统。基于实际专利案例和用户自定义撰写规范创建，
  提供完整的专利撰写工作流程，包括技术分析、文档撰写、质量控制等功能。
author: 基于用户专利案例和撰写指令
bmad_version: "4.0.0"

# 扩展包类型和领域
type: domain-specific
domain: intellectual-property
category: legal-tech

# 文件清单
files:
  agents:
    - patent-writing-orchestrator.md  # 李明华 - 专利撰写项目协调员
    - patent-analyst.md              # 张技术 - 专利分析专家
    - patent-writer.md               # 王文档 - 专利撰写专家
    - patent-reviewer.md             # 陈审查 - 专利质量审查员
    - patent-critic.md               # 严教授 - 严格批判审查专家

  data:
    - patent-law-basics.md           # 专利法基础知识
    - patent-writing-standards.md    # 专利撰写标准和规范
    - technical-writing-principles.md # 技术文档撰写原则

  tasks:
    # 核心工具任务 (必需 - 从bmad-core复制)
    - create-doc.md                  # 文档创建任务
    - execute-checklist.md           # 检查清单执行任务
    # 专利特定任务
    - analyze-technical-disclosure.md # 技术交底书分析
    - search-prior-art.md            # 现有技术检索
    - draft-claims.md                # 权利要求书起草
    - write-specification.md         # 说明书撰写
    - write-abstract.md              # 摘要撰写
    - review-patent-quality.md       # 专利质量审查
    - critical-review.md             # 严格批判性审查
    - format-validation.md           # 格式规范验证

  utils:
    # 核心工具 (必需 - 从bmad-core复制)
    - template-format.md             # 模板格式规范
    - workflow-management.md         # 工作流管理系统
    - writing-constraints.md         # 写作限制规范（字数控制和修改标记）

  templates:
    - patent-application-tmpl.md     # 完整专利申请书模板 (LLM指令嵌入)
    - claims-tmpl.md                 # 权利要求书模板 (条件内容和变量系统)
    - specification-tmpl.md          # 说明书模板 (技术方案详细描述)
    - abstract-tmpl.md               # 摘要模板 (简洁准确的技术概述)
    - technical-disclosure-analysis-tmpl.md # 技术交底书分析模板

  checklists:
    - patent-quality-checklist.md    # 综合专利质量检查清单 (三级验证)
    - claims-quality-checklist.md    # 权利要求书专用检查清单
    - format-compliance-checklist.md # 格式合规性检查清单

  workflows:
    - patent-writing-workflow.md     # 专利撰写主工作流 (决策树和交接协议)

  agent-teams:
    - patent-team.yml               # 专利撰写团队配置

# 用户必须提供的数据文件 (放置在bmad-core/data/目录)
required_user_data:
  - filename: patent-writing-guidelines.md
    description: 用户特定的专利撰写规范和标准要求
    format: Markdown文档，包含撰写规范、格式要求、质量标准
    example: 功能性描述句式、"所述"使用规范、技术术语标准
    validation: 检查是否包含完整的撰写规范和示例

  - filename: technical-terminology.md
    description: 特定技术领域的专业术语库和表达方式
    format: Markdown文档，按技术领域分类的术语表
    example: 医疗设备术语、电子技术术语、AI算法术语
    validation: 确认术语定义准确，表达方式标准

  - filename: patent-examples.md
    description: 参考的专利案例和最佳实践示例
    format: Markdown文档，包含完整的专利案例分析
    example: 成功的权利要求书案例、说明书撰写范例
    validation: 验证案例的完整性和参考价值

# 嵌入的领域知识 (自动包含在扩展包中)
embedded_knowledge:
  - patent-law-basics.md            # 专利法基础知识和法律要求
  - patent-writing-standards.md     # 基于实际案例的撰写标准
  - technical-writing-principles.md # 技术文档撰写原则和最佳实践

# 对核心BMAD组件的依赖
core_dependencies:
  agents:
    - architect        # 用于系统设计和架构规划
    - developer       # 用于技术实现和代码开发
    - qa-specialist   # 用于质量保证和测试验证
  tasks:
    - advanced-elicitation # 高级需求收集技术
    - create-deep-research-prompt # 深度研究提示生成
  workflows:
    - brownfield-create-epic # 现有项目史诗创建
    - brownfield-create-story # 现有项目故事创建

# 代理协调模式
agent_coordination:
  orchestrator: patent-writing-orchestrator  # 主协调员
  handoff_protocols: true                     # 启用交接协议
  numbered_options: true                      # 使用编号选项交互
  quality_integration: comprehensive          # 综合质量集成
  workflow_management: structured             # 结构化工作流管理
  user_confirmation_required: true            # 修改专利文件需要用户确认
  concise_writing: true                       # 强制精简写作，严格字数限制
  modification_marking: true                  # 修改内容标记机制

# 支持的功能特性
capabilities:
  document_creation:
    - patent-application-drafting    # 专利申请书起草
    - claims-writing                # 权利要求书撰写
    - specification-writing         # 说明书撰写
    - abstract-writing             # 摘要撰写
  
  analysis_functions:
    - technical-disclosure-analysis # 技术交底书分析
    - prior-art-search             # 现有技术检索
    - patentability-assessment     # 专利性评估
    - infringement-analysis        # 侵权风险分析
  
  quality_control:
    - multi-level-validation       # 多级质量验证
    - format-compliance-check      # 格式合规性检查
    - legal-compliance-review      # 法律合规性审查
    - star-rating-system          # 星级评分系统
  
  workflow_management:
    - project-coordination         # 项目协调管理
    - task-assignment             # 任务分配
    - progress-tracking           # 进度跟踪
    - team-collaboration          # 团队协作
    - user-confirmation-control   # 用户确认控制机制
    - concise-writing-control     # 精简写作控制
    - modification-marking        # 修改内容标记

# 支持的专利类型
patent_types:
  - invention-patent              # 发明专利
  - utility-model                # 实用新型专利
  - design-patent                # 外观设计专利

# 支持的技术领域
technical_fields:
  - medical-devices              # 医疗设备
  - electronic-systems           # 电子系统
  - flexible-electronics         # 柔性电子
  - data-processing              # 数据处理
  - ai-algorithms                # AI算法
  - mechanical-engineering       # 机械工程
  - biotechnology               # 生物技术
  - chemical-engineering        # 化学工程

# 质量保证级别
quality_levels:
  basic:
    description: 基础格式和语言规范检查
    coverage: 格式规范性、语言准确性
  comprehensive:
    description: 深入的技术和法律合规性检查
    coverage: 技术方案完整性、法律合规性、保护范围合理性
  expert:
    description: 高级的策略性和专业性评估
    coverage: 专利策略、商业价值、维权可行性

# 安装后消息
post_install_message: |
  🎯 专利撰写扩展包安装完成！

  📋 主协调员: 李明华 (patent-writing-orchestrator)
  👥 专家团队: 4名专业代理 (分析、撰写、审查、严格批判)
  📝 智能模板: 5个带LLM指令嵌入的专业模板
  ✅ 质量保证: 三级验证系统，星级评分标准

  📋 必需用户数据文件 (请放置在 bmad-core/data/ 目录):
  - patent-writing-guidelines.md: 您的专利撰写规范和标准
  - technical-terminology.md: 特定技术领域的专业术语库
  - patent-examples.md: 参考的专利案例和最佳实践

  🚀 快速开始:
  1. 准备上述数据文件
  2. 运行: @patent-writing-orchestrator
  3. 遵循李明华的编号选项指导
  4. 享受专业的专利撰写服务

  🎨 核心特性:
  - 严格遵循专利撰写规范 ("所述"指代、"用于"句式等)
  - 基于实际专利案例优化 (CN111388003B、CN117159019B等)
  - 智能模板系统 (条件内容、动态变量、LLM指导)
  - 多级质量验证 (基础/综合/专家三级检查)
  - 用户确认机制 (任何修改都需要用户明确同意)
  - 精简写作控制 (严格字数限制，避免冗余解释)
  - 修改标记机制 (修改内容用"**修改提示**"标记，保留原文)

  📖 嵌入知识库:
  - 专利法基础知识和法律要求
  - 基于实际案例的撰写标准
  - 技术文档撰写原则和最佳实践

# 版本信息
version_info:
  release_date: "2024-01-01"
  compatibility: "BMAD v4.0.0+"
  last_updated: "2024-01-08"
  changelog: "v1.2.0 - 新增精简写作和修改标记机制：1)严格字数限制，避免冗余解释；2)修改内容用'**修改提示**'标记，保留原文"
