# 创建文档任务

## 任务概述

这是专利撰写扩展包的核心文档创建任务，用于根据模板生成各种专利申请文件。

## 支持的文档类型

### 1. 专利申请文件
- `patent-application-tmpl` - 完整的专利申请书
- `claims-tmpl` - 权利要求书
- `specification-tmpl` - 说明书
- `abstract-tmpl` - 摘要

### 2. 分析文件
- `technical-disclosure-analysis-tmpl` - 技术交底书分析

## 使用方法

### 基本语法
```
*create-doc [template-name]
```

### 示例
```
*create-doc claims-tmpl
*create-doc patent-application-tmpl
*create-doc specification-tmpl
```

## 任务执行流程

### 步骤1：模板选择
- 如果用户指定了模板名称，直接使用该模板
- 如果用户未指定模板，显示可用模板列表供选择

### 步骤2：模板加载
- 从templates目录加载指定的模板文件
- 解析模板中的LLM指令和变量占位符
- 准备模板格式化工具

### 步骤3：用户引导
- 根据模板中的LLM指令，逐步引导用户填写内容
- 提供专业的撰写建议和规范要求
- 处理条件内容和动态变量

### 步骤4：内容生成
- 根据用户输入替换模板变量
- 应用条件逻辑生成相应内容
- 确保格式规范和语言标准

### 步骤5：质量检查
- 提醒用户使用相应的质量检查清单
- 提供初步的格式和内容验证
- 建议后续的审查步骤

## 模板变量系统

### 基本变量
- `{{variable_name}}` - 简单变量替换
- `{{invention_name}}` - 发明名称
- `{{applicant_name}}` - 申请人名称
- `{{technical_field}}` - 技术领域

### 条件内容
```
^^CONDITION: condition_name^^
条件为真时显示的内容
^^/CONDITION: condition_name^^
```

### 重复内容
```
<<REPEAT section="section_name" count="{{count_variable}}">>
重复的内容模板
<</REPEAT>>
```

## LLM指令处理

### 指令类型
- `[[LLM: 指导说明]]` - 给AI的指导指令
- 在文档生成过程中，这些指令用于指导AI如何与用户交互

### 指令功能
- 提供撰写指导和建议
- 解释专业术语和要求
- 引导用户完成复杂的填写过程
- 确保符合专利撰写规范

## 质量控制

### 自动检查
- 变量完整性检查
- 格式规范验证
- 基本语法检查

### 用户提醒
- 提醒使用相应的检查清单
- 建议专业审查步骤
- 提供改进建议

## 错误处理

### 常见错误
1. 模板文件不存在
2. 变量未正确替换
3. 条件逻辑错误
4. 格式不符合要求

### 解决方案
1. 检查模板文件路径和名称
2. 确认所有必要变量已填写
3. 验证条件逻辑的正确性
4. 使用格式检查工具验证

## 输出格式

### 标准输出
- 生成的文档内容
- 格式化的专利申请文件
- 符合官方要求的文档结构

### 附加信息
- 使用的模板名称
- 填写完成度统计
- 质量检查建议
- 下一步操作指导

## 最佳实践

### 撰写前准备
1. 准备完整的技术交底书
2. 了解相关技术领域的专业术语
3. 收集参考的专利案例

### 撰写过程
1. 严格按照模板指导进行
2. 确保技术描述的准确性
3. 遵循专利撰写规范

### 撰写后检查
1. 使用质量检查清单验证
2. 进行多轮审查和修改
3. 确保与其他文档的一致性

## 相关工具

### 依赖工具
- `template-format` - 模板格式化工具
- `workflow-management` - 工作流管理工具

### 相关任务
- `review-patent-quality` - 质量审查任务
- `format-validation` - 格式验证任务
- `execute-checklist` - 检查清单执行任务

## 技术支持

如遇到问题，请：
1. 检查模板文件是否存在
2. 确认变量填写是否完整
3. 参考相关的质量检查清单
4. 联系专业的专利代理师进行审查
