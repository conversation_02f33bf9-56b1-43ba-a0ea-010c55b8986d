# 模板格式规范

## 概述

本文档定义了专利撰写扩展包中模板文件的格式规范和标记约定，确保模板的一致性和可维护性。

## 基本格式结构

### 文件头部
```markdown
# 模板名称

[[LLM: 模板用途和使用说明]]

## 模板说明
[模板的详细说明和使用指导]
```

### 内容区域
```markdown
---

## 主要内容

[模板的主体内容]

---

## 使用指导

[使用该模板的具体指导]
```

## 变量标记系统

### 基本变量
**格式**: `{{variable_name}}`

**用途**: 简单的文本替换

**示例**:
```markdown
发明名称: {{invention_name}}
申请人: {{applicant_name}}
技术领域: {{technical_field}}
```

### 描述性变量
**格式**: `{{descriptive_variable_name}}`

**命名规范**:
- 使用下划线分隔单词
- 变量名应具有描述性
- 避免使用缩写

**示例**:
```markdown
{{technical_problem_description}}
{{solution_overview}}
{{beneficial_effects_list}}
```

## 条件内容标记

### 基本条件语法
```markdown
^^CONDITION: condition_name^^
条件为真时显示的内容
^^/CONDITION: condition_name^^
```

### 条件类型

#### 专利类型条件
```markdown
^^CONDITION: patent_type == "invention"^^
发明专利特有的内容
^^/CONDITION: patent_type^^

^^CONDITION: patent_type == "utility_model"^^
实用新型专利特有的内容
^^/CONDITION: patent_type^^
```

#### 权利要求类型条件
```markdown
^^CONDITION: claim_type == "system"^^
系统类权利要求的内容
^^/CONDITION: claim_type^^

^^CONDITION: claim_type == "method"^^
方法类权利要求的内容
^^/CONDITION: claim_type^^
```

#### 技术领域条件
```markdown
^^CONDITION: technical_field == "medical_device"^^
医疗设备领域特有的内容
^^/CONDITION: technical_field^^
```

### 复合条件
```markdown
^^CONDITION: patent_type == "invention" AND claim_type == "system"^^
发明专利的系统类权利要求内容
^^/CONDITION: patent_type AND claim_type^^
```

## 重复内容标记

### 基本重复语法
```markdown
<<REPEAT section="section_name" count="{{count_variable}}">>
重复的内容模板
<</REPEAT>>
```

### 重复类型

#### 权利要求重复
```markdown
<<REPEAT section="dependent_claim" count="{{dependent_claims_count}}">>
**{{claim_number}}. 根据权利要求{{reference_claim}}所述的{{invention_type}}，其特征在于，{{additional_limitation}}。**
<</REPEAT>>
```

#### 步骤重复
```markdown
<<REPEAT section="method_step" count="{{steps_count}}">>
步骤{{step_number}}、{{step_description}}；
<</REPEAT>>
```

#### 组件重复
```markdown
<<REPEAT section="system_component" count="{{components_count}}">>
{{component_name}}，用于{{component_function}}；
<</REPEAT>>
```

## LLM指令标记

### 基本指令格式
```markdown
[[LLM: 指令内容]]
```

### 指令类型

#### 引导指令
```markdown
[[LLM: 引导用户填写发明名称，要求简洁明确，体现技术特点]]
```

#### 检查指令
```markdown
[[LLM: 检查用户输入的技术方案是否完整，提醒补充遗漏的技术特征]]
```

#### 建议指令
```markdown
[[LLM: 建议用户参考相关技术领域的专利案例，确保术语使用准确]]
```

#### 验证指令
```markdown
[[LLM: 验证权利要求的逻辑结构，确保从属权利要求正确引用独立权利要求]]
```

### 多行指令
```markdown
[[LLM: 这是一个多行指令示例：
1. 首先引导用户理解技术方案
2. 然后协助用户填写相关内容
3. 最后提供质量检查建议]]
```

## 专利特定标记

### 权利要求编号
```markdown
**{{claim_number}}. {{claim_content}}**
```

### 技术特征描述
```markdown
{{component_name}}，用于{{component_function}}
```

### 步骤描述
```markdown
步骤{{step_number}}、{{step_action}}{{step_details}}
```

### 引用关系
```markdown
根据权利要求{{reference_claim_number}}所述的{{invention_type}}
```

## 格式化规范

### 标题层级
```markdown
# 一级标题 - 模板名称
## 二级标题 - 主要章节
### 三级标题 - 子章节
#### 四级标题 - 详细说明
```

### 列表格式
```markdown
- 无序列表项
  - 子列表项
  
1. 有序列表项
   1. 子列表项
```

### 代码块
```markdown
```yaml
代码内容
```
```

### 强调格式
```markdown
**粗体文本**
*斜体文本*
`代码文本`
```

## 注释和说明

### 模板注释
```markdown
<!-- 这是模板注释，不会在最终文档中显示 -->
```

### 填写说明
```markdown
[填写说明：这里需要填写具体的技术内容]
```

### 示例内容
```markdown
@{example: 示例标题}
示例内容
@{/example}
```

## 质量控制标记

### 必填项标记
```markdown
{{*required_field*}} - 必填项
{{optional_field}} - 可选项
```

### 验证标记
```markdown
{{field_name|validation_rule}} - 带验证规则的字段
```

### 依赖关系标记
```markdown
{{field_name|depends_on:other_field}} - 依赖其他字段的字段
```

## 最佳实践

### 变量命名
1. 使用描述性名称
2. 保持命名一致性
3. 避免使用特殊字符
4. 使用下划线分隔单词

### 条件逻辑
1. 保持条件简单明确
2. 避免过度嵌套
3. 提供默认内容
4. 测试所有条件分支

### LLM指令
1. 指令应该清晰具体
2. 提供足够的上下文
3. 包含错误处理指导
4. 考虑用户体验

### 模板维护
1. 定期更新模板内容
2. 保持格式一致性
3. 测试模板功能
4. 收集用户反馈

## 错误处理

### 常见错误
1. 变量名拼写错误
2. 条件语法错误
3. 重复标记不匹配
4. LLM指令格式错误

### 调试方法
1. 检查标记语法
2. 验证变量引用
3. 测试条件逻辑
4. 确认指令格式

### 错误预防
1. 使用标准化命名
2. 遵循格式规范
3. 进行充分测试
4. 建立审查流程
