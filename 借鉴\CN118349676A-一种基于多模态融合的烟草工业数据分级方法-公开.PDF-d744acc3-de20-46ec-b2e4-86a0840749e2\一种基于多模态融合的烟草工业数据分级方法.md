# (19)国家知识产权局

# (12)发明专利申请

(10)申请公布号CN118349676A(43)申请公布日2024.07.16

(21)申请号202410463125.1

(22)申请日2024.04.17

(71)申请人安徽中烟工业有限责任公司地址230088安徽省合肥市高新区黄山路606号

(72)发明人叶为全王莉汪飞裴文浩孔俊苏明陈凯杨秉宇魏振春谢骥薛训明

(74)专利代理机构安徽省合肥新安专利代理有限责任公司34101

专利代理师陆丽莉何梅生

(51)Int.Cl.

G06F16/35(2019.01) G06F40/30(2020.01) G06F18/25(2023.01)

G06N3/045(2023.01) G06N3/0464(2023.01) G06N3/0499(2023.01) G06N3/09(2023.01)

权利要求书3页 说明书7页 附图2页

# (54)发明名称

一种基于多模态融合的烟草工业数据分级方法

# (57)摘要

本发明公开了一种基于多模态融合的烟草工业数据分级方法，包括：1将多模态烟草工业文本数据和图像数据输入基于Transformer的分级网络中处理；2、将处理后的结果输入模态特征提取器中提取各模态内的特征；3、将提取出的烟草图片特征和文本特征输入多模态特征融合模块中提取模态间特征；4、将图像结合特征和文本结合特征输入分级器得到预测类别概率向量，并选取最大概率值所对应的类别作为预测标签；5、利用预测标签和真实标签构建交叉损失，并对基于Transformer的分级网络进行训练和优化。本发明能很好地发掘多模态数据之间的耦合关系，从而能精准地判断多模态烟草数据的密级，降低人力成本。

![](images/c43fec6d62b249afdb02fa4b4bb437736e1d8cae7c4104d5f0adde0112696751.jpg)

1. 一种基于多模态融合的烟草工业数据分级方法, 其特征在于, 是按如下步骤进行:

步骤1、构建多模态烟草工业数据集  $\mathrm{A} = \{\mathrm{T}, \mathrm{P}\}$ , 其中, T表示非结构化的烟草文本数据集; P表示烟草图像数据集; 将任意一个多模态烟草工业数据记为  $\mathrm{x} = \{\mathrm{t}, \mathrm{p}\}$ , 其中, t表示任意一个烟草文本数据, p表示任意一个烟草图像; 令  $\mathrm{x}$  的真实标签为  $\mathrm{y}; \mathrm{y} \in \{1,2, \dots , \mathrm{C}\}$ , C表示敏感度等级;

步骤2、构建基于Transformer的分级网络, 包括: 嵌入层、模态特征提取器、多模态特征融合模块和一个分级器;

步骤2.1、所述嵌入层由两个词向量嵌入层和一个位置嵌入层组成, 并用于对烟草文本数据t和烟草图像p进行处理, 相应得到t的嵌入特征Pt和p的嵌入特征Pp;

步骤2.2、所述模态特征提取器由N个相同的模态特征提取块堆叠组成; 其中, 每个模态特征提取块包含一个多头自注意力层和一个前馈网络层;

步骤2.2.1、所述模态特征提取器对Pp进行处理, 输出烟草图像p的模态内特征向量  $\mathrm{R} = \{\mathrm{r}_{1}, \mathrm{r}_{2}, \ldots , \mathrm{r}_{\mathrm{i}}, \ldots , \mathrm{r}_{\mathrm{n}}\}$ ; 从而计算烟草图像p的聚合特征  $\mathrm{i}_{0} = \frac{1}{n} \sum_{i = 1}^{n} \mathrm{r}_{i}$  其中,  $\mathrm{ri}$  表示第i个图像块的特征;

步骤2.2.2、所述模态特征提取器对于Pt进行处理, 从而得到烟草文本数据t的模态内特征向量  $\mathrm{E} = \{\mathrm{e}_{1}, \mathrm{e}_{2}, \ldots , \mathrm{e}_{\mathrm{i}}, \ldots , \mathrm{e}_{\mathrm{n}}\}$ , 其中,  $\mathrm{e}_{\mathrm{i}}$  表示第i个语段的特征;

步骤2.2.3、使用窗口大小为1的卷积对文本嵌入特征  $\mathrm{E} = \{\mathrm{e}_{1}, \mathrm{e}_{2}, \ldots , \mathrm{e}_{\mathrm{i}}, \ldots , \mathrm{e}_{\mathrm{n}}\}$  进行处理, 得到文本t的卷积特征  $\mathrm{p}_{1,1}, \mathrm{p}_{1,2}, \dots , \mathrm{p}_{1,\mathrm{i}}, \dots , \mathrm{p}_{1,\mathrm{n}}$ ; 从而计算窗口大小为1的最大池化特征  $\mathrm{q}_{1} = \max \left(\mathrm{p}_{1,1}, \mathrm{p}_{1,2}, \dots , \mathrm{p}_{1,\mathrm{n}}\right)$ ; 其中,  $\mathrm{p}_{1,1}$  表示对  $\mathrm{e}_{\mathrm{i}}$  进行窗口大小为1的卷积操作后, 得到的卷积特征;

步骤2.2.4、改变窗口大小1的值L次, 并返回步骤2.2.3处理, 从而得到多尺度卷积特征  $\{\mathrm{q}_{1} \mid 1 = 1,2, \dots , \mathrm{L}\}$ ;

步骤2.2.5、利用式(2)得到烟草文本数据t的模态内特征的最终表示  $\mathrm{c}_{0}$ :

$$
\mathrm{c}_{0} = \mathrm{LayerNorm}\left(\mathrm{W}_{\mathrm{e}} \mathrm{concat}\left(\mathrm{q}_{1}, \mathrm{q}_{2}, \dots , \mathrm{q}_{1}, \dots , \mathrm{q}_{\mathrm{L}}\right) + \mathrm{b}_{\mathrm{e}}\right) \tag{2}
$$

式(2)中,  $\mathrm{W}_{\mathrm{e}}$  是卷积滤波矩阵,  $\mathrm{b}_{\mathrm{e}}$  是偏差;

步骤2.3、所述多模态特征融合模块包含: 一个自注意力层、一个平均池化层、一个最大池化层、一个融合自注意力模块, 并对叠加特征  $Z = \left( \begin{array}{c} R \\ E \end{array} \right) = \{r_{1}, \ldots , r_{n}; e_{1}, \ldots , e_{n}\}$  进行处理, 得到烟草图像p的最终嵌入  $\mathrm{i}_{1}$  和烟草文本数据t的最终嵌入  $\mathrm{c}_{1}$ ;

步骤2.4、分级器对烟草图像p的结合特征  $\mathrm{i} = \mathrm{i}_{0} + \mathrm{i}_{1}$  和烟草文本数据t的结合特征  $\mathrm{c} = \mathrm{c}_{0} + \mathrm{c}_{1}$  进行处理后, 得到x的预测类别概率向量, 并选取最大概率值所对应的类别作为x的预测标签  $\mathrm{y}^{\prime}$ ;

步骤3、基于y和y'构建交叉损失, 并利用Adam优化器对所述基于Transformer的分级网络进行训练, 计算所述交叉损失以更新网络参数, 直至迭代次数达到最大迭代次数时或交叉损失值达到最小时, 停止训练, 从而得到训练后的最优分级网络模型, 用于对输入的多模态烟草工业数据集进行敏感度的分级。

2. 根据权利要求1所述的一种基于多模态融合的烟草工业数据分级方法, 其特征在于, 所述步骤2.1包括:

步骤2.1.1、第一个词向量嵌入层对烟草文本数据t进行词转换处理，得到t的词向量表示  $(\mathfrak{t}_{\mathrm{cls}},\mathfrak{t}_1,\mathfrak{t}_2,\ldots ,\mathfrak{t}_{\mathrm{i}},\ldots ,\mathfrak{t}_{\mathrm{n}},\mathfrak{t}_{\mathrm{s cp}})$  ；其中，  $\mathfrak{t}_{\mathrm{cls}}$  表示词转换的开头，  $\mathfrak{t}_{\mathrm{s cp}}$  表示词转换的结尾，  $\mathfrak{t}_{\mathrm{i}}$  表示第i个语段的词向量；n表示词向量的个数；

第二个词向量嵌入层将烟草图像p划分成n个图像块后，再通过线性映射将每个图像块映射到一维向量中，从而得到p的词向量表示  $(\mathfrak{p}_1,\mathfrak{p}_2,\ldots ,\mathfrak{p}_{\mathrm{i}}\ldots ,\mathfrak{p}_{\mathrm{n}})$  ；其中，  $\mathfrak{p}_{\mathrm{i}}$  表示第i个图像块的词向量；

步骤2.1.2、所述位置嵌入层对t的词向量表示和p的词向量表示进行位置编码，分别得到t的嵌入特征  $\mathrm{P t} = \{\mathrm{w}_{0} + \mathrm{t}_{\mathrm{c l s}} + \mathrm{w}_{1} + \mathrm{t}_{1},\mathrm{w}_{2} + \mathrm{t}_{2},\ldots ,\mathrm{w}_{\mathrm{i}} + \mathrm{t}_{\mathrm{i}},\ldots ,\mathrm{w}_{\mathrm{n}} + \mathrm{t}_{\mathrm{n}} + \mathrm{w}_{\mathrm{n} + 1} + \mathrm{t}_{\mathrm{s c p}}\}$  和p的嵌入特征Pp $\mathbf{\omega} = \{\mathbf{w}_1 + \mathbf{p}_1,\mathbf{w}_2 + \mathbf{p}_2,\ldots ,\mathbf{w}_1 + \mathbf{p}_1,\ldots ,\mathbf{w}_n + \mathbf{p}_n\}$  ；其中，  $\mathbf{w}_{\mathrm{i}}$  表示词向量中第1个位置的位置编码。

3. 根据权利要求1所述的一种基于多模态融合的烟草工业数据分级方法，其特征在于，所述步骤2.2.1包括：

步骤2.2.1.1、当s=1时，定义第s个模态特征提取块的输入为  $\mathrm{X}_{\mathrm{s}} = \mathrm{Pp}$

步骤2.2.1.2、第s个模态特征提取块中的第j头自注意力层通过不同的线性变换对  $\mathrm{X}_{\mathrm{s}}$  进行处理，得到第s个模态特征提取块的第j个查询向量  $Q_{s,j} = W_{s,j}^{Q}\cdot P_{p}$  、第s个模态特征提取块的第j个键向量  $K_{s,j} = W_{s,j}^{K}\cdot P_{p}$  、第s个模态特征提取块的第j个值向量  $V_{s,j} = W_{s,j}^{V}\cdot P_{p}$  ，其中， $W_{s,j}^{Q}$  、  $W_{s,j}^{K}$  、  $W_{s,j}^{V}$  分别表示第s个模态特征提取块中的第j头自注意力层中三种线性变换所对应的权值矩阵；

第s个模态特征提取器中的第j头自注意力层利用式(1)得到烟草图像p的第j个烟草图像特征  $\mathrm{head}_{\mathrm{s,j}}$

$$
\mathrm{head}_{s,j} = soft\max (\frac{Q_{s,j}(K_{s,j})' + SparsityMask(Q_{s,j},(K_{s,j})')}{\sqrt{d}})V_{s,j} \tag{1}
$$

式(1)中，d表示Pp的维度，SparsityMask是用于输出稀疏性掩码的神经网络，sofmax表示激活函数；  $(\mathrm{K}_{\mathrm{s,j}})$  '表示  $\mathrm{Ks}$  ，j的转置；

步骤2.2.1.3、将第s个模态特征提取块中所有自注意力头输出的烟草图像特征进行聚合后，得到第s个聚合烟草图像特征  $\mathrm{head}_{\mathrm{s}}$  ，将  $\mathrm{head}_{\mathrm{s}}$  输入前馈神经网络中进行处理后，得到第s个模态特征提取块输出的特征值  $\mathrm{f}_{\mathrm{s}}$

步骤2.2.1.4、若s≥N，则将第N个模态特征提取块输出的特征值f作为模态特征提取器输出的烟草图像p的模态内特征向量  $\mathrm{R} = \{\mathrm{r}_1,\mathrm{r}_2\ldots \mathrm{r}_i\ldots \mathrm{r}_n\}$  ；否则，将s+1赋值给s，并令  $\mathrm{X}_{\mathrm{s}} = \mathrm{f}_{\mathrm{s} - 1}$  后，返回步骤2.2.1.2顺序执行。

4. 根据权利要求1所述的一种基于多模态融合的烟草工业数据分级方法，其特征在于，所述步骤2.3包括：

步骤2.3.1、将叠加特征  $Z = \left( \begin{array}{c}R\\ E \end{array} \right) = \{r_1,\dots,r_n;e_1,\dots,e_n\}$  输入多模态特征融合模块中，并由所述自注意力层通过三种不同的线性变换后，得到键向量  $K_{Z} = Z\cdot W^{K} = \binom{K_{R}}{K_{E}}$  ，查询向量

$Q_{Z} = Z\cdot W^{Q} = \binom{Q_{R}}{Q_{E}}$  ，值向量  $V_{Z} = Z\cdot W^{V} = \binom{V_{R}}{V_{E}}$  ，从而利用式(3)得到多模态融合自注意力值  $Y = \left( \begin{array}{c}P_{c}\\ T_{c} \end{array} \right)$  其中，  $\mathrm{K}_{\mathrm{R}}$  表示烟草文本数据t的键向量，  $\mathrm{K}_{\mathrm{E}}$  表示烟草图像p的键向量，  $\mathrm{Q}_{\mathrm{R}}$  表示烟草文本数据t的查询向量，  $\mathrm{Q}_{\mathrm{E}}$  表示烟草图像p的查询向量，  $\mathrm{V}_{\mathrm{R}}$  表示烟草文本数据t的值向量，  $\mathrm{V}_{\mathrm{E}}$  表示烟草图像p的值向量，  $\mathrm{W}^Q,\mathrm{W}^K,\mathrm{W}^V$  分别为三种不同的权重矩阵，  $\mathrm{P}_{\mathrm{c}}$  表示烟草图像p的交叉特征，  $\mathrm{T}_{\mathrm{c}}$  表示烟草文本数据t的交叉特征；

$$
Y = Sof tmax \left(\frac{\left( \begin{array}{c}Q_{R}\\ Q_{E} \end{array} \right)\left(K_{R}^{T}K_{E}^{T}\right)}{\sqrt{d}}\right)\left( \begin{array}{c}V_{R}\\ V_{E} \end{array} \right) \tag{3}
$$

式(3)中，sofmax表示激活函数；

步骤2.3.2、所述平均池化层对  $\mathrm{P}_{\mathrm{c}}$  进行处理，得到图像池化特征V；

步骤2.3.3、所述最大池化层对  $\mathrm{T}_{\mathrm{c}}$  进行处理，得到文本池化特征M；

步骤2.3.4、所述融合自注意力模块分别将V和M进行处理后，得到烟草图像p的最终嵌入i和烟草文本数据t的最终嵌入  $\mathrm{c}_{1}$  。

5. 一种电子设备，包括存储器以及处理器，其特征在于，所述存储器用于存储支持处理器执行权利要求1-4中任一所述烟草工业数据分级方法的程序，所述处理器被配置为用于执行所述存储器中存储的程序。

6. 一种计算机可读存储介质，计算机可读存储介质上存储有计算机程序，其特征在于，所述计算机程序被处理器运行时执行权利要求1-4中任一所述烟草工业数据分级方法的步骤。

# 一种基于多模态融合的烟草工业数据分级方法

# 技术领域

[0001] 本发明属于人工智能领域，具体的说是一种基于多模态融合的烟草工业数据分级方法。

# 背景技术

[0002] 在我国国家政策的大力支持下，数字化应用正在引领各垂直化领域变革，这种趋势下，数据流动将产生越来越多的价值。然而数据的使用也是一把双刃剑，只要数据处于流动的过程中就会存在敏感数据泄露的风险。随着信息技术的迅猛发展，数字化时代的烟草业务数据资源也在不断增长，呈现出庞大规模、多样结构、复杂关联的特点。烟草业的数据包括生产、供应链、销售、市场推广等多个方面，因其特殊性，很多数据既有商业价值的因素，也有国家强管控的背景，有较强的数据敏感性要求。同时，烟草行业作为国家的战略性产业，其产生的数据中包含了大量的商业机密、经营决策、合作伙伴信息等重要敏感数据。如果对这些敏感数据无法进行有效的管理和保护，可能导致敏感信息泄露，进而对烟草企业自身的商业竞争力、声誉和经济利益造成严重威胁，甚至影响烟草供应链的正常运作，对国家烟草产业和社会稳定造成不良影响。

[0003] 卷烟数字化工业企业要提高数据的使用价值，避免敏感数据泄露产生的不良后果，就需要在汇聚共享海量数据的同时，找出企业敏感数据所在，并根据敏感程度情况分类规范和建立数据分类分级保护机制。

[0004] 目前烟草敏感数据识别和分类分级方式主要分为三类：一是人工识别方法，通常由专业水平的业务人员，通过经验对每个数据进行分类分级。由于烟草企业内部各个部门业务的差异性和相关性，员工往往难以明确判断现有业务数据的敏感程度，且由于主观判断存在个体差异性，引起敏感数据分类分级不一致和不准确的问题。

[0005] 二是规则匹配方式，通常将数据与建立好的关键词库做关键词匹配，如果包含这些关键词，就可以将其标记为敏感数据，同时辅以正则表达式和元数据分析，协助敏感数据的分类。规则匹配系统可能因为规则定义不准确或数据变化不及时而产生误报、漏报的情况，且一些敏感数据的识别需要考虑复杂的上下文信息，而规则匹配系统有时难以理解和处理这些复杂性，例如，同样的关键词在不同的上下文中可能有不同的含义，这可能会导致敏感数据分级不准确。

[0006] 三是人工智能的方法，使用通用的算法模型，通过大量数据的训练达到对敏感数据识别并分级的效果。当模型变复杂时，容易产生过拟合现象，对敏感数据的分类分级准确率会大大降低，因此现有的一些基于人工智能方法的敏感数据分类分级产品在分类颗粒度和精度方面存在一定的局限性，同时满足用户更高级和定制化需求的能力也不足。同时，随着数据类型和格式的多样化，目前的敏感数据分类分级产品在处理非结构化数据时也存在一定的缺陷。当前的数据呈现出文本、图片、视频和音频等多个模态相互融合的趋势，现在的产品对于不同类型的非结构化数据使用不同类型的人工智能模型进行处理，而单一模态的模型在处理其中某一种模态的数据时往往会忽略另一种模态中所包含的信息，不能将几

种模态的信息融合处理，也不能充分挖掘敏感信息。

# 发明内容

[0007] 本发明是为了解决上述现有技术存在的不足之处，提出一种基于多模态融合的烟草工业数据分级方法，以期能够很好地发掘多模态数据之间的耦合关系，从而能精准地判断烟草数据的敏感等级，降低人力成本。

[0008] 本发明为达到上述发明目的，采用如下技术方案：

[0009] 本发明一种基于多模态融合的烟草工业数据分级方法的特点在于，是按如下步骤进行：

[0010] 步骤1、构建多模态烟草工业数据集A={T，P}，其中，T表示非结构化的烟草文本数据集；P表示烟草图像数据集；将任意一个多模态烟草工业数据记为x={t，p}，其中，t表示任意一个烟草文本数据，p表示任意一个烟草图像；令x的真实标签为y；y∈{1,2，，C}，C表示敏感度等级；

[0011] 步骤2、构建基于Transformer的分级网络，包括：嵌入层、模态特征提取器、多模态特征融合模块和一个分级器；

[0012] 步骤2.1、所述嵌入层由两个词向量嵌入层和一个位置嵌入层组成，并用于对烟草文本数据t和烟草图像p进行处理，相应得到t的嵌入特征Pt和p的嵌入特征Pp；

[0013] 步骤2.2、所述模态特征提取器由N个相同的模态特征提取块堆叠组成；其中，每个模态特征提取块包含一个多头自注意力层和一个前馈网络层；

[0014] 步骤2.2.1、所述模态特征提取器对Pp进行处理，输出烟草图像p的模态内特征向量R={r，r，r，r}；从而计算烟草图像p的聚合特征i=∑=1r;其中，r表示第i个图像块的特征；

[0015] 步骤2.2.2、所述模态特征提取器对于Pt进行处理，从而得到烟草文本数据t的模态内特征向量E={e,e，，e，，e}，其中，e表示第i个语段的特征；

[0016] 步骤2.2.3、使用窗口大小为1的卷积对文本嵌入特征E={e,e，，e}进行处理，得到文本t的卷积特征p1,1，p1,2，，p1,i，，p1,n；从而计算窗口大小为1的最大池化特征q=max(p1,1，p1,2，，p1,n)；其中，p表示对e进行窗口大小为1的卷积操作后，得到的卷积特征；

[0017] 步骤2.2.4、改变窗口大小1的值L次，并返回步骤2.2.3处理，从而得到多尺度卷积特征{q|l=1,2，L}；

[0018] 步骤2.2.5、利用式(2)得到烟草文本数据t的模态内特征的最终表示c0：

[0019]  $\mathrm{c}_0 = \mathrm{LayerNorm}\left(\mathrm{W}_\mathrm{e}\mathrm{concat}\left(\mathrm{q}_1,\mathrm{q}_2,\ldots ,\mathrm{q}_1,\ldots \mathrm{q}_\mathrm{L}\right) + \mathrm{b}_\mathrm{e}\right)$  (2)

[0020] 式(2)中，W是卷积滤波矩阵，b是偏差；

[0021] 步骤2.3、所述多模态特征融合模块包含：一个自注意力层、一个平均池化层、一个最大池化层、一个融合自注意力模块，并对叠加特征  $Z = \binom{R}{E} = \{r_1,\ldots ,r_n;e_1,\ldots ,e_n\}$  进行处理，得到烟草图像p的最终嵌入i和烟草文本数据t的最终嵌入c；

[0022] 步骤2.4、分级器对烟草图像p的结合特征i=i0+i和烟草文本数据t的结合特征c

$= c_{0} + c_{1}$  进行处理后, 得到x的预测类别概率向量, 并选取最大概率值所对应的类别作为x的预测标签y'；

[0023] 步骤3、基于y和y构建交叉熵损失, 并利用Adam优化器对所述基于Transformer的分级网络进行训练, 计算所述交叉熵损失以更新网络参数, 直至迭代次数达到最大迭代次数时或交叉熵损失值达到最小时, 停止训练, 从而得到训练后的最优分级网络模型, 用于对输入的多模态烟草工业数据集进行敏感度的分级。

[0024] 本发明所述的一种基于多模态融合的烟草工业数据分级方法的特点也在于, 所述步骤2.1包括:

[0025] 步骤2.1.1、第一个词向量嵌入层对烟草文本数据t进行词转换处理, 得到t的词向量表示  $(\mathrm{t}_{\mathrm{cls}}, \mathrm{t}_{1}, \mathrm{t}_{2}, \ldots , \mathrm{t}_{\mathrm{n}}, \ldots , \mathrm{t}_{\mathrm{n}}, \mathrm{t}_{\mathrm{scp}})$ ; 其中,  $\mathrm{t}_{\mathrm{cls}}$  表示词转换的开头,  $\mathrm{t}_{\mathrm{scp}}$  表示词转换的结尾,  $\mathrm{t}_{\mathrm{i}}$  表示第i个语段的词向量; n表示词向量的个数;

[0026] 第二个词向量嵌入层将烟草图像p划分成n个图像块后, 再通过线性映射将每个图像块映射到一维向量中, 从而得到p的词向量表示  $(\mathrm{p}_{1}, \mathrm{p}_{2}, \ldots , \mathrm{p}_{\mathrm{i}}, \ldots , \mathrm{p}_{\mathrm{n}})$ ; 其中,  $\mathrm{p}_{\mathrm{i}}$  表示第i个图像块的词向量;

[0027] 步骤2.1.2、所述位置嵌入层对t的词向量表示和p的词向量表示进行位置编码, 分别得到t的嵌入特征  $\mathrm{Pt} = \{\mathrm{w}_0 + \mathrm{t}_{\mathrm{cls}} + \mathrm{w}_1 + \mathrm{t}_1, \mathrm{w}_2 + \mathrm{t}_2, \ldots , \mathrm{w}_{\mathrm{i}} + \mathrm{t}_{\mathrm{i}}, \ldots , \mathrm{w}_{\mathrm{n}} + \mathrm{t}_{\mathrm{n}} + \mathrm{w}_{\mathrm{n + 1}} + \mathrm{t}_{\mathrm{scp}}\}$  和p的嵌入特征  $\mathrm{Pp} = \{\mathrm{w}_1 + \mathrm{p}_1, \mathrm{w}_2 + \mathrm{p}_2, \ldots , \mathrm{w}_{\mathrm{i}} + \mathrm{p}_{\mathrm{i}}, \ldots , \mathrm{w}_{\mathrm{n}} + \mathrm{p}_{\mathrm{n}}\}$ ; 其中,  $\mathrm{w}_{\mathrm{i}}$  表示词向量中第i个位置的位置编码。

[0028] 所述步骤2.2.1包括:

[0029] 步骤2.2.1.1、当s=1时, 定义第s个模态特征提取块的输入为  $\mathrm{X}_{\mathrm{s}} = \mathrm{P}_{\mathrm{p}}$ ;

[0030] 步骤2.2.1.2、第s个模态特征提取块中的第j头自注意力层通过不同的线性变换对  $\mathrm{X}_{\mathrm{s}}$  进行处理, 得到第s个模态特征提取块的第j个查询向量  $Q_{s,j} = W_{s,j}^{Q} \cdot P_{p}$  、第s个模态特征提取块的第j个键向量  $K_{s,j} = W_{s,j}^{K} \cdot P_{p}$  、第s个模态特征提取块的第j个值向量  $V_{s,j} = W_{s,j}^{V} \cdot P_{p}$ , 其中,  $W_{s,j}^{Q}$  、  $W_{s,j}^{K}$  、  $W_{s,j}^{V}$  分别表示第s个模态特征提取块中的第j头自注意力层中三种线性变换所对应的权值矩阵;

[0031] 第s个模态特征提取器中的第j头自注意力层利用式(1)得到烟草图像p的第j个烟草图像特征  $\mathrm{head}_{\mathrm{s},\mathrm{j}}$ :

$$
\mathrm{head}_{\mathrm{s},j} = \operatorname {softmax}\left(\frac{Q_{s,j}\left(K_{s,j}\right)^{\prime} + \operatorname{SparsityMask}\left(Q_{s,j}\left(K_{s,j}\right)^{\prime}\right)}{\sqrt{d}}\right)_{\mathrm{M}_{\mathrm{s},j}} \tag{1}
$$

[0033] 式(1)中, d表示Pp的维度, SparsityMask是用于输出稀疏性掩码的神经网络, soformax表示激活函数;  $\left(K_{\mathrm{s},\mathrm{j}}\right)^{\prime}$  表示  $\mathrm{K}_{\mathrm{s},\mathrm{j}}$  的转置;

[0034] 步骤2.2.1.3、将第s个模态特征提取块中所有自注意力头输出的烟草图像特征进行聚合后, 得到第s个聚合烟草图像特征  $\mathrm{head}_{\mathrm{s}}$ , 将  $\mathrm{head}_{\mathrm{s}}$  输入前馈神经网络中进行处理后, 得到第s个模态特征提取块输出的特征值  $\mathrm{f}_{\mathrm{s}}$ ;

[0035] 步骤2.2.1.4、若s≥N, 则将第N个模态特征提取块输出的特征值  $\mathrm{f}_{\mathrm{N}}$  作为模态特征提取器输出的烟草图像p的模态内特征向量  $\mathrm{R} = \{\mathrm{r}_{1}, \mathrm{r}_{2}, \ldots , \mathrm{r}_{\mathrm{i}}, \ldots , \mathrm{r}_{\mathrm{n}}\}$ ; 否则, 将s+1赋值给s, 并令  $\mathrm{X}_{\mathrm{s}} = \mathrm{f}_{\mathrm{s - 1}}$  后, 返回步骤2.2.1.2顺序执行。

[0036] 所述步骤2.3包括：

[0037] 步骤2.3.1、将叠加特征  $Z = \binom{R}{E} = \{r_1,\ldots ,r_n;e_1,\ldots ,e_n\}$  输入多模态特征融合模块中，并由所述自注意力层通过三种不同的线性变换后，得到键向量  $K_{Z} = Z\cdot W^{K} = \binom{K_{R}}{K_{E}}$  ，查询向量  $Q_{Z} = Z\cdot W^{Q} = \binom{Q_{R}}{Q_{E}}$  ，值向量  $V_{Z} = Z\cdot W^{V} = \binom{V_{R}}{V_{E}}$  ，从而利用式(3)得到多模态融合自注意力值  $Y = \left( \begin{array}{c}P_{c}\\ T_{c} \end{array} \right)$  ，其中，  $\mathrm{K}_{\mathrm{R}}$  表示烟草文本数据t的键向量，  $\mathrm{K}_{\mathrm{E}}$  表示烟草图像p的键向量，  $\mathrm{Q}_{\mathrm{R}}$  表示烟草文本数据t的查询向量，  $\mathrm{Q}_{\mathrm{E}}$  表示烟草图像p的查询向量，  $\mathrm{V}_{\mathrm{R}}$  表示烟草文本数据t的值向量，  $\mathrm{V}_{\mathrm{E}}$  表示烟草图像p的值向量，  $\mathrm{W}^{\mathrm{Q}},\mathrm{W}^{\mathrm{K}},\mathrm{W}^{\mathrm{V}}$  分别为三种不同的权重矩阵，  $\mathrm{P}_{\mathrm{c}}$  表示烟草图像p的交叉特征，  $\mathrm{T}_{\mathrm{c}}$  表示烟草文本数据t的交叉特征；

$$
Y = S o f t m a x\left(\frac{\left(Q_{R}\right)\left(K_{R}^{T}K_{E}^{T}\right)}{\sqrt{d}}\right)\left(V_{R}\right) \tag{3}
$$

[0039] 式(3)中，softmax表示激活函数；

[0040] 步骤2.3.2、所述平均池化层对  $\mathrm{P}_{\mathrm{c}}$  进行处理，得到图像池化特征V；

[0041] 步骤2.3.3、所述最大池化层对  $\mathrm{T}_{\mathrm{c}}$  进行处理，得到文本池化特征M；

[0042] 步骤2.3.4、所述融合自注意力模块分别将V和M进行处理后，得到烟草图像p的最终嵌入i和烟草文本数据t的最终嵌入  $\mathrm{c}_{\mathrm{T}}$  。

[0043] 本发明一种电子设备，包括存储器以及处理器的特点在于，所述存储器用于存储支持处理器执行所述烟草工业数据分级方法的程序，所述处理器被配置为用于执行所述存储器中存储的程序。

[0044] 本发明一种计算机可读存储介质，计算机可读存储介质上存储有计算机程序的特点在于，所述计算机程序被处理器运行时执行所述烟草工业数据分级方法的步骤。

[0045] 与现有技术相比，本发明的有益效果在于：

[0046] 1、本发明基于transformer模型进行特征表示学习，能够捕捉文本数据的全局语义信息和局部语义特征；通过模型微调和预测，能够对烟草文本数据进行高效准确的分级；这种分级方法可以提高分级的准确性和效率，为烟草系统信息安全管理提供可靠的决策支持。

[0047] 2、本发明使用稀疏的多头自注意力机制去捕捉语料之间的依赖耦合，极大地降低了算法的时间复杂度和空间复杂度。在烟草行业的信息处理领域，这种优化意味着能够处理更大规模的数据集，同时保持高效的运行速度和较低的资源消耗。这对于需要实时或近实时处理大量文本数据的烟草信息系统而言，是一个重大的技术进步，可以显著提升系统的响应速度和处理能力。

[0048] 3、本发明通过预处理和特征处理将不同类型的数据转化为可供模型处理的特征

向量，并通过不同模态的特征融合综合利用不同模态的数据信息，提高了分级的综合性和全面性，使模型更好地理解和描述烟草数据的背景和特征，提高了烟草分级的精度和鲁棒性。

# 附图说明

[0049] 图1为本发明中一种基于多模态融合的烟草工业数据分级方法流程图；[0050] 图2为本发明中基于多模态融合的烟草工业数据分级方法的模型结构图。

# 具体实施方式

[0051] 在本实施例中，如图1所示，一种基于多模态融合的烟草工业数据分级方法是按如下步骤进行：

[0052] 步骤1、构建多模态烟草工业数据集A={T,P}，其中，T表示非结构化的烟草文本数据集；P表示烟草图像数据集；将任意一个多模态烟草工业数据记为x={t,p}，其中，t表示任意一个烟草文本数据，p表示任意一个烟草图像；令x的真实标签为y；y∈{1,2，，C}，C表示敏感度等级；

[0053] 步骤2、如图2所示，构建基于Transformer的分级网络，包括：嵌入层、模态特征提取器、多模态特征融合模块和一个分级器；

[0054] 步骤2.1、嵌入层由两个词向量嵌入层和一个位置嵌入层组成；

[0055] 步骤2.1.1、第一个词向量嵌入层对烟草文本数据t进行词转换处理，得到t的词向量表示  $(\mathrm{t}_{\mathrm{cls}},\mathrm{t}_1,\mathrm{t}_2,\ldots ,\mathrm{t}_i,\ldots ,\mathrm{t}_{\mathrm{n}},\mathrm{t}_{\mathrm{scp}})$  ；其中，  $\mathrm{t}_{\mathrm{cls}}$  表示词转换的开头，  $\mathrm{t}_{\mathrm{scp}}$  表示词转换的结尾，t表示第i个语段的词向量；n表示词向量的个数；

[0056] 第二个词向量嵌入层将烟草图像p划分成n个图像块后，再通过线性映射将每个图像块映射到一维向量中，从而得到p的词向量表示  $(\mathrm{p}_1,\mathrm{p}_2,\ldots ,\mathrm{p}_i,\ldots ,\mathrm{p}_n)$  ；其中，  $\mathrm{p}_i$  表示第i个图像块的词向量；

[0057] 步骤2.1.2、位置嵌入层对t的词向量表示和p的词向量表示进行位置编码，分别得到t的嵌入特征  $\mathrm{Pt} = \{\mathrm{w}_0 + \mathrm{t}_{\mathrm{cls}} + \mathrm{w}_1 + \mathrm{t}_1,\mathrm{w}_2 + \mathrm{t}_2,\ldots ,\mathrm{w}_{\mathrm{i}} + \mathrm{t}_{\mathrm{i}},\ldots ,\mathrm{w}_{\mathrm{n}} + \mathrm{t}_{\mathrm{n}} + \mathrm{w}_{\mathrm{n} + 1} + \mathrm{t}_{\mathrm{scp}}\}$  和p的嵌入特征Pp  $= \{\mathrm{w}_1 + \mathrm{p}_1,\mathrm{w}_2 + \mathrm{p}_2,\ldots ,\mathrm{w}_{\mathrm{i}} + \mathrm{p}_{\mathrm{i}},\ldots ,\mathrm{w}_{\mathrm{n}} + \mathrm{p}_{\mathrm{n}}\}$  ；其中，  $\mathrm{w}_{\mathrm{i}}$  表示词向量中第i个位置的位置编码。

[0058] 步骤2.2、模态特征提取器由N个相同的模态特征提取块堆叠组成；每个模态特征提取块包含一个多头自注意力层和一个前馈网络层；

[0059] 步骤2.2.1、模态特征提取器对Pp进行处理，输出烟草图像s的模态内特征向量R={r1,r2. ..r;其中，r表示第i个图像块的特征；

[0060] 步骤2.2.1.1、当s=1时，定义第s个模态特征提取块的输入为X=Pp；

[0061] 步骤2.2.1.2、第s个模态特征提取块中的第j头自注意力层通过不同的线性变换对X进行处理，得到第s个模态特征提取块的第j个查询向量Qsj=WsP、第s个模态特征提取块的第j个键向量Ksj=WsP、第s个模态特征提取块的第j个值向量Vsj=WsP，其中，Ws≠W≠W分别表示三种线性变换所对应的权值矩阵；

[0062] 第s个模态特征提取器中的第j头自注意力层利用式(1)得到烟草图像p的第j个烟

草图像特征  $\mathrm{head}_{s,j}$  : 稀疏的多头自注意力机制有助于降低时间复杂度, 并通过有选择地关注序列中的关键位置捕获长距离依赖, 而不是仅限于局部上下文, 从而提高了对长序列依赖关系的建模能力。

$$
\mathrm{head}_{s,j} = \operatorname {softmax}\left(\frac{Q_{s,j}\left(K_{s,j}\right)^{\prime} + \operatorname{SparsityMask}\left(Q_{s,j}\left(K_{s,j}\right)^{\prime}\right)}{\sqrt{d}}\right)V_{s,j} \tag{1}
$$

[0064]式(1)中，d表示Pp的维度，SparsityMask是用于输出稀疏性掩码的神经网络，这个网络会根据输入的  $\mathbb{Q}_{s,j}$  和  $(\mathrm{K}_{s,j})^{\prime}$  算出一个稀疏性掩码，softmax表示激活函数；  $(\mathrm{K}_{s,j})^{\prime}$  表示 $\mathrm{K}_{s,j}$  的转置；

[0065]步骤2.2.1.3、将第s个模态特征提取块中所有自注意力头的输出聚合得到  $\mathrm{head}_{s}$  随后  $\mathrm{head}_{s}$  经前馈神经网络处理后得到第s个模态特征提取块的值  $\mathrm{f}_{s}$

[0066]步骤2.2.1.4、步骤2.2.1.4、若  $\mathrm{s}\geq \mathrm{N}$  ，则将第N个模态特征提取块输出的特征值  $\mathrm{f}_{\mathrm{N}}$  作为模态特征提取器输出的烟草图像p的模态内特征向量  $\mathrm{R} = \{\mathrm{r}_1,\mathrm{r}_2\dots \mathrm{r}_i\dots \mathrm{r}_n\}$  ；否则，将s $^{+1}$  赋值给s，并令  $\mathrm{X}_{\mathrm{s}} = \mathrm{f}_{\mathrm{s}^{- 1}}$  后，返回步骤2.2.1.2顺序执行；

[0067]步骤2.2.1.5、包含图像中区域与区域关系的特征向量E通过平均池化操作聚合图像区域的特征表示得到  $\begin{array}{r}i_0 = \frac{1}{n}\sum_{i = 1}^{n}r_i \end{array}$

[0068]步骤2.2.2、按照步骤2.2.1.1- 步骤2.2.1.4的过程对于文本数据Pt进行处理，即可得到烟草文本的模态内特征向量  $\mathrm{E} = \{\mathrm{e}_1,\mathrm{e}_2,\ldots \mathrm{e}_i,\ldots \mathrm{e}_n\}$  ，其中，  $\mathrm{e}_i$  表示第i个语段的特征；

[0069]步骤2.2.3、使用窗口大小为1的卷积对文本嵌入特征  $\mathrm{E} = \{\mathrm{e}_1,\mathrm{e}_2,\ldots ,\mathrm{e}_i,\ldots ,\mathrm{e}_n\}$  进行处理，得到文本t的卷积特征  $\mathrm{p}_{1,1},\mathrm{p}_{1,2},\ldots ,\mathrm{p}_{1,\mathrm{i}},\ldots ,\mathrm{p}_{1,\mathrm{n}}$  ；其中，  $\mathrm{p}_{1,\mathrm{i}}$  表示在第i个位置进行窗口大小为1卷积操作得到的卷积特征。

[0070] 计算最大池化特征  $\mathrm{q}_1 = \max \left(\mathrm{p}_{1,1},\mathrm{p}_{1,2},\ldots ,\mathrm{p}_{1,\mathrm{n}}\right)$

[0071]步骤2.2.4、改变窗口大小1的值L次，并返回步骤2.2.3处理，从而得到多尺度卷积特征  $\{\mathrm{q}_1\mid 1 = 1,2,\dots \mathrm{L}\}$

[0072]步骤2.2.5、利用式(2)得到文本数据模态内特征的最终表示  $\mathrm{c}_0$

[0073]  $\mathrm{c}_0 = \mathrm{LayerNorm}\left(\mathrm{W}_\mathrm{e}\mathrm{concat}\left(\mathrm{q}_1,\mathrm{q}_2,\mathrm{q}_3\right) + \mathrm{b}_\mathrm{e}\right)$  (2)

[0074]式(2)中，  $\mathrm{W}_{\mathrm{e}}$  是卷积滤波矩阵，  $\mathrm{b}_{\mathrm{e}}$  是偏差；

[0075]步骤2.3、多模态特征融合模块包含：一个自注意力层、一个平均池化层、一个最大池化层、一个融合自注意力模块；

[0076]步骤2.3.1、将叠加特征  $Z = \binom{R}{E} = \{r_1,\ldots ,r_n;e_1,\ldots ,e_n\}$  输入多模态特征融合模块中，

自注意力层通过三种不同的线性变换得到键向量  $K_{Z} = Z\cdot W^{K} = \binom{K_{R}}{K_{E}}$  ，查询向量 $Q_{Z} = Z\cdot W^{Q} = \binom{Q_{R}}{Q_{E}}$  ，值向量  $V_{Z} = Z\cdot W^{V} = \binom{V_{R}}{V_{E}}$  ，从而利用式(3)得到多模态融合自注意力

值  $Y = \left( \begin{array}{c}P_{c}\\ T_{c} \end{array} \right)$ ，其中， $\mathrm{K}_{\mathrm{R}}$  表示文本数据的键向量， $\mathrm{K}_{\mathrm{E}}$  表示图像数据的键向量， $\mathrm{Q}_{\mathrm{R}}$  表示文本数据的查询向量， $\mathrm{Q}_{\mathrm{E}}$  表示图像数据的查询向量， $\mathrm{V}_{\mathrm{R}}$  表示文本数据的值向量， $\mathrm{W}^{\mathrm{Q}},\mathrm{W}^{\mathrm{K}},\mathrm{W}^{\mathrm{V}}$  分别为不同的权重矩阵， $\mathrm{P}_{\mathrm{c}}$  表示烟草图像交叉特征， $\mathrm{T}_{\mathrm{c}}$  表示烟草文本交叉特征；融合自注意力模块通过关联和整合不同源的信息和动态关注机制提高了模型的性能和效率，并且提供了更好的可解释性。

$$
Y = Softmax\left(\frac{\left(Q_R\right)\left(K_R^TK_E^T\right)}{\sqrt{d}}\right)\left(V_R\right) \tag{3}
$$

[0078] 式(3)中，softmax表示激活函数；

[0079] 步骤2.3.2、平均池化层对  $\mathrm{P}_{\mathrm{c}}$  进行处理，得到图像池化特征V；

[0080] 步骤2.3.3、最大池化层对  $\mathrm{T}_{\mathrm{c}}$  进行处理，得到文本池化特征M；

[0081] 步骤2.3.4、融合自注意力模块分别将V和M进行处理后，得到图像的最终嵌入i和文本的最终嵌入  $c_{1}$  。

[0082] 步骤2.4、分级器对图像结合特征  $\mathrm{i} = \mathrm{i}_{0} + \mathrm{i}_{1}$  和文本结合特征  $\mathrm{c} = \mathrm{c}_{0} + \mathrm{c}_{1}$  进行处理后。得到x的预测类别概率向量，并选取最大概率值所对应的类别作为x的预测标签y；

[0083] 步骤3、基于y和y'构建交叉损失，并利用Adam优化器对基于Transformer的分级网络进行训练，计算交叉损失以更新网络参数，直至迭代次数达到最大迭代次数时或交叉损失值达到最小时，停止训练，从而得到训练后的最优分级网络模型，用于对输入的多模态烟草工业数据集进行敏感度的分级。

[0084] 本实施例中，一种电子设备，包括存储器以及处理器，该存储器用于存储支持处理器执行上述方法的程序，该处理器被配置为用于执行该存储器中存储的程序。

[0085] 本实施例中，一种计算机可读存储介质，是在计算机可读存储介质上存储有计算机程序，该计算机程序被处理器运行时执行上述方法的步骤。

![](images/60b50ebe1c49f252a5d867bb76069d7e579ded9a5206a7f1903d8c1591d1919d.jpg)  
图1

![](images/454c10edd621fc2c321380da893eca32fbfcb9cd14eb1c653e4b05c0d02a94b8.jpg)  
图2