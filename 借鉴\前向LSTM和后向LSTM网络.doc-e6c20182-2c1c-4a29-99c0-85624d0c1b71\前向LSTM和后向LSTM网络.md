1. 一种基于上下文建模的宫颈细胞全切片的分类方法，其特征是按如下步骤进行：

步骤1、获取  $T$  类维度为  $H\times W\times C$  的宫颈细胞全切片样本并进行归一化预处理，得到预处理后的全切片序列并作为训练样本，记为  $S = \left[S_{1},S_{2},\dots ,S_{t},\dots ,S_{T}\right]$  ，其中，  $S_{i}$  表示第  $t$  类归一化后的宫颈细胞全切片样本，且  $S_{t} = \left\{P_{1}^{t},P_{2}^{t},\dots ,P_{k}^{t},\dots ,P_{K}^{t}\right\}$  ，  $P_{k}^{t}$  表示第  $t$  类归一化后的宫颈细胞全切片样本  $S_{t}$  中的第  $k$  个全切片，  $H$  表示高度、  $W$  表示宽度、  $C$  表示通道数；  $K$  表示第  $t$  类归一化后的宫颈细胞全切片样本  $S_{t}$  的总切片数；

采用非重叠分割方式将第  $k$  个全切片  $P_{k}^{t}$  分割成多个图像块，得到分块集合记为 $P_{k}^{t} = \left\{W_{1}^{t,k},W_{2}^{t,k},\dots ,W_{n}^{t,k},\dots ,W_{N_{k}}^{t,k}\right\} ,W_{n}^{t,k}$  表示第  $k$  个全切片  $P_{k}^{t}$  的第  $n$  个分块图像；  $N_{k}$  表示第  $k$  个全切片  $P_{k}^{t}$  的总块数；

步骤2、搭建基于FasterRcnn网络的细胞检测和提取网络，用于固定大小的细胞检测和特征提取；

步骤2.1、搭建由骨干网络、区域提取网络、ROI检测网络、分类网络构成的细胞检测和提取网络；

步骤2.1.1、所述骨干网络是基于ResNet101网络，并依次包括：第一卷积块、第二卷积块、第三卷积块、第四卷积块、第五卷积块和第六分类块；

将所述第  $n$  个分块图像  $W_{n}^{t,k}$  输入所述骨干网络中进行处理，依次经过五个卷积块后输出

特征图  $Q_{n}^{t,k}$ ；

步骤2.1.2、所述区域提取网络在所述特征图  $Q_{n}^{t,k}$  上生成各个尺度和长宽比的锚点框，并通过Softmax分类器判定各个锚点框内是前景还是背景，并将判定为前景的锚点框作为目标候选区域，再利用边框回归图修正所述目标候选区域，从而得到多个精准候选区域并输出；

步骤2.1.3、所述ROI检测网络利用ROI池化层将多个精准候选区域映射到所述特征图 $Q_{n}^{t,k}$  上，再利用全连接层和Softmax分类器构成的分类网络依次对特征图  $Q_{n}^{t,k}$  上的精准候选区域进行判断，若判断为细胞核图像，则提取特征图  $Q_{n}^{t,k}$  上相应精准候选区域并作为细胞核特征图，从而得到细胞核特征图集合  $I_{n}^{t,k} = \left\{I_{1}^{t,k,n},I_{2}^{t,k,n},\dots ,I_{j}^{t,k,n},\dots ,I_{J_{t,k,n}}^{t,k,n}\right\} ,I_{j}^{t,k,n}$  表示第  $k$  个全切片  $P_{k}^{t}$  的第  $n$  个分块图像  $W_{n}^{t,k}$  的特征图  $Q_{n}^{t,k}$  中第  $j$  个细胞核特征图；  $J_{t,k,n}$  表示特征图  $Q_{n}^{t,k}$  的细胞核特征图总数；

将所述第  $j$  个细胞核特征图  $I_{j}^{t,k,n}$  输入至骨干提取网络的第六分类块中，输出第  $j$  个细胞核特征向量  $F_{j}^{t,k,n}$ ，从而得到  $J_{t,k,n}$  个细胞核特征向量并构成细胞核特征序列  $F_{n}^{t,k} = \left\{F_{1}^{t,k,n},F_{2}^{t,k,n},\dots ,F_{j}^{t,k,n},\dots ,F_{J_{t,k,n}}^{t,k,n}\right\}$ ；

步骤3、构造上下文建模网络，包括：双向长短期记忆网络Bi- LSTM、注意力机制模块和融合模块：

步骤3.1、构造双向长短期记忆网络；

步骤3.1.1、所述双向长短期记忆网络Bi- LSTM，包括：前向LSTM和后向LSTM，用于对输入的细胞核特征序列  $F_{n}^{t,k}$  进行特征学习；

将第  $j$  个细胞核特征向量  $F_{j}^{t,k,n}$  分别输入前向LSTM和后向LSTM中，相应得到  $a$  时刻前向LSTM的隐状态输出为  $h_{a,j}^{t,k,n}$  和后向LSTM的隐状态输出为  $h_{a,j}^{t,\mathrm{LUL}}$  ，并拼接为  $a$  时刻双向长短期记忆网络Bi- LSTM的输出  $h_{a,j}^{t,k,n} = \left[h_{a,j}^{t,k,n}h_{a,j}^{t,k,n}\right]$  ，从而得到  $a$  时刻的隐状态集合 $H_{a}^{t,k,n} = [h_{a,1}^{t,k,n},h_{a,2}^{t,k,n},\dots ,h_{a,j}^{t,k,n},\dots ,h_{a,j_{k,n}}^{t,k,n}]$

步骤3.2、所述注意力机制模块，包括：一个最大池化层、一个全连接层和一个回归函数;将第  $j$  个细胞核特征向量  $F_{j}^{t,k,n}$  注意力机制模块中，并依次经过最大池化层、全连接层后，得到注意力特征向量  $M_{j}^{t,k,n}$  ，从而得到注意力特征序列  $M_{n}^{t,k} = \left\{M_{1}^{t,k,n},M_{2}^{t,k,n},\dots ,M_{j}^{t,k,n},\dots ,M_{J_{t,k,n}}^{t,k,n}\right\}$  将所述输入注意力特征向量  $M_{j}^{t,k,n}$  输入回归函数中，从而利用式(1)得到第  $j$  个细胞核特征向量 $F_{j}^{t,k,n}$  的权重  $O_{j}^{t,k,n}$  ，从而得到权重集合  $O_{n}^{t,k} = \{O_{1}^{t,k,n},O_{2}^{t,k,n},\dots ,O_{j}^{t,k,n},\dots ,O_{J_{t,k,n}}^{t,k,n}\}$

$$
O_{j}^{t,k,n} = \frac{exp\left[\left(\mathbf{M}_{j}^{t,k,n}\right)^{\prime}\tanh \left(F_{j}^{t,k,n}\times \mathbf{V}\right)\right]}{\sum_{j = 1}^{J_{t,k,n}}exp\left[\left(\mathbf{M}_{j}^{t,k,n}\right)^{\prime}\tanh \left(F_{j}^{t,k,n}\times \mathbf{V}\right)\right]} \tag{1}
$$

式(1)中，  $\left(\mathbf{M}_{j}^{t,k,n}\right)^{\prime}$  表示输入注意力特征向量  $M_{j}^{t,k,n}$  的转置，  $\mathbf{V}^{\prime}$  表示矩阵数乘

$M_{\tilde{\mathcal{G}}}^{t,k,n}\times M_{j}^{t,k,n}\times \dots \times M_{j_{\mathrm{t}}}^{t,k,n}$  的转置，  $d$  表示超参数，  $T$  表示细胞类别数；  $\tanh ()$  表示正切曲线函

数；

步骤3.3、所述融合模块将所述隐状态集合  $H_{a}^{t,k,n}$  和所述权重集合  $O_{n}^{t,k}$  结合起来并输入至tanh 激活函数中得到特征融合信息序列  $R_{n}^{t,k}$  ；再将特征融合信息序列  $R_{n}^{t,k}$  序列输入一个全连接层中，从而得到第  $n$  个分块图像  $W_{n}^{t,k}$  的分类结果；

步骤4、利用式(2)建立KL散度损失函数  $Z_{KL}$

$$
Z_{KL} = -\sum_{t}^{T}(u_{k}^{t}\times \log (\mathsf{v}_{k}^{t}) - u_{k}^{t}\times \log (u_{k}^{t}))
$$

式(2)中，  $u_{k}^{t}$  表示第  $k$  个全切片  $P_{k}^{t}$  是第  $t$  类的真值得分，  $\mathbf{v}_{k}^{t}$  表示融合模块输出的第  $k$  个全切片  $P_{k}^{t}$  是第  $t$  类的预测概率得分；

将所述训练样本中各个全切片的所有图像块输入所述细胞检测和提取网络以及上下文建模网络进行训练，并通过Adam优化器不断优化KL散度损失函数  $Z_{KL}$ ，以调整网络参数，从而得到宫颈细胞全切片分类器，用于实现宫颈细胞全切片的分类结果。