# (19)国家知识产权局

# (12)发明专利

(21)申请号202410724016.0

(22)申请日2024.06.05

(65)同一申请的已公布的文献号申请公布号CN118745883A

(43)申请公布日2024.10.08

(73)专利权人延长油田股份有限公司地址717208陕西省延安市延川县永坪镇

(72)发明人宁涛席萌刘小高彦斌王晓辉权伟李键郝锦涛王振龙崔强

(74)专利代理机构北京万新知识产权代理有限公司16195

专利代理师汤文旋

(51)Int.Cl.E21B43/20(2006.01)E21B49/00(2006.01)

# (54)发明名称

一种油田注水系统能耗优化方法及装置

# (57)摘要

本发明提供一种油田注水系统能耗优化方法及装置，涉及油田注水系统能耗技术领域，具体步骤包括：S1. 采集油田注水系统中的水处理系统数据、水泵系统数据和注水井系统数据，其中，所述水处理系统数据包括水质参数和环境参数，所述水质参数包括pH值和悬浮物含量，所述环境参数包括温度和湿度，所述水泵系统数据包括管道压力、流体流速、管道的直径和运行时间，所述注水井系统数据包括井口压力参数和地层参数。本发明通过采集油田注水系统中的水处理系统数据、水泵系统数据和注水井系统数据，可以全面和准确评估油田注水系统能耗，还可以确定优化的优先顺序，继而优先对能耗较大的系统优化，使得优化工作更加有针对性和高效。

(10)授权公告号CN118745883B(45)授权公告日2025.01.21

E21B47/06(2012.01)F04B51/00(2006.01)F04B49/06(2006.01)G06F30/18(2020.01)G06F30/28(2020.01)C02F1/00(2023.01)G01D21/02(2006.01)G01M99/00(2011.01)G06F113/08(2020.01)G06F113/14(2020.01)G06F119/14(2020.01)C02F103/10(2006.01)

# (56)对比文件

CN110795893A,2020.02.14 CN110821456A,2020.02.21

审查员朱钰荣

权利要求书3页说明书11页附图2页

![](images/5d06c910f8723bf50cac0611f893ae7a657f40878a938c090561b7ef093545fe.jpg)

1. 一种油田注水系统能耗优化方法，其特征在于，具体步骤包括：

S1. 采集油田注水系统中的水处理系统数据、水泵系统数据和注水井系统数据，其中，所述水处理系统数据包括水质参数和环境参数，所述水质参数包括pH值和悬浮物含量，所述环境参数包括温度和湿度，所述水泵系统数据包括管道压力、流体流速、管道的直径和运行时间，所述注水井系统数据包括井口压力参数和地层参数，所述井口压力参数包括注水井口压力、注水井的注水量和油井口压力，所述地层参数包括地层渗透率和孔隙度；

S2. 将水质参数和环境参数进行数据处理和分析，生成水处理系统系数，将水处理系统系数和水处理系统的运行能耗建立线性回归方程，依据水处理系统系数，获取水处理系统的运行能耗；

S3. 将水泵系统数据进行数据处理和分析，生成水泵系统压力系数，依据水泵系统压力系数，生成水泵系统的运行能耗；

S4. 将注水井系统数据进行数据处理和分析，生成注水井系统压力系数，依据注水井系统压力系数，生成注水井系统的运行能耗；

S5. 将水处理系统的运行能耗和水处理系统的历史平均运行能耗进行数据处理，生成水处理系统的能耗差值，将水泵系统的运行能耗和水泵系统的运行能耗的历史平均运行能耗进行数据处理，生成水泵系统的能耗差值，将注水井系统的运行能耗和注水井系统的历史平均运行能耗进行数据处理，生成注水井系统的能耗差值；

S6. 比较水处理系统的能耗差值、水泵系统的能耗差值和注水井系统的能耗差值大小，根据差值大小的顺序，确定油田注水系统优化的顺序。

2. 根据权利要求1所述的油田注水系统能耗优化方法，其特征在于：将水质参数和环境参数进行数据处理和分析，生成水处理系统系数，依据的公式如下：

$$
S = \frac{pH\times T}{H} +\sqrt{\frac{C}{pH\times H}}
$$

其中，S为水处理系统系数，表示水质参数和环境参数的综合影响，pH为水的pH值，指水中氢离子浓度的负对数，T为水处理系统的环境温度，H为水处理系统的环境湿度，C为水中的悬浮固体颗粒的含量。

3. 根据权利要求2所述的油田注水系统能耗优化方法，其特征在于：将水处理系统系数和水处理系统的运行能耗建立线性回归方程，依据水处理系统系数，获取水处理系统的运行能耗的过程如下：

$$
\mathrm{E}_1 = \beta_0\cdot \beta_1\cdot \mathrm{S} + \mathrm{\delta}
$$

其中，E为水处理系统的运行能耗，S表示水处理系统系数，  $\beta_0$  为截距项，  $\beta_{1}$  为线性回归模型的系数，  $\delta$  为误差项。

4. 根据权利要求1所述的油田注水系统能耗优化方法，其特征在于：将水泵系统数据进行数据处理和分析，生成水泵系统压力系数，依据的公式如下：

$$
P = \frac{\sum_{i = 1}^{n}(P_d\cdot V\cdot e^{-\alpha_i\cdot D}\cdot YS)}{\sqrt[n]{\beta_1}}\times \gamma
$$

其中，P为水泵系统压力系数，  $\mathrm{P_d}$  为管道压力，V为流体流速，  $\mathrm{D}$  为管道直径，YS为运行时

间,  $\alpha_{i}$  为第i个样本的管道直径的系数,  $\beta_{1}$  为压力系数的数量级,  $\gamma$  为常数项, n为4。

5. 根据权利要求4所述的油田注水系统能耗优化方法, 其特征在于: 依据水泵系统压力系数, 生成水泵系统的运行能耗, 依据的公式如下:

$$
\mathrm{E}_2 = \mathrm{k}\cdot \mathrm{P}
$$

其中,  $\mathrm{E}_2$  为水泵系统的运行能耗, k为比例系数, 表示水泵系统压力系数P对水泵系统的运行能耗  $\mathrm{E}_2$  的影响程度。

6. 根据权利要求1所述的油田注水系统能耗优化方法, 其特征在于: 将注水井系统数据进行数据处理和分析, 生成注水井系统压力系数, 依据的公式如下:

$$
Z = \frac{\sum_{i = 1}^{m}\left(\frac{P_{w}\cdot Q_{w}}{P_{o}}\cdot\frac{r_{i}^{2}\cdot\phi}{\sqrt{r_{i} + \sqrt{\phi}}}\right)}{\sqrt[n]{\beta_{2}}}
$$

其中, Z为注水井系统压力系数,  $\mathrm{P}_{\mathrm{w}}$  为注水井口压力,  $\mathrm{Q}_{\mathrm{w}}$  为注水井的注水量,  $\mathrm{P}_{\mathrm{o}}$  为油井口压力,  $\mathrm{r}_{\mathrm{i}}$  为第i个样本地层渗透率,  $\phi$  为地层孔隙度,  $\beta_{2}$  为注水井系统压力系数的数量级, m为5。

7. 根据权利要求6所述的油田注水系统能耗优化方法, 其特征在于: 依据注水井系统压力系数, 生成注水井系统的运行能耗, 依据的公式如下:

$$
E_{3} = \frac{1}{Z^{2} + \mu}
$$

其中,  $\mathrm{E}_3$  为注水井系统的运行能耗,  $\mu$  为常数。

8. 根据权利要求1所述的油田注水系统能耗优化方法, 其特征在于: 将水处理系统的运行能耗和水处理系统的历史平均运行能耗进行数据处理, 生成水处理系统的能耗差值, 将水泵系统的运行能耗和水泵系统的运行能耗的历史平均运行能耗进行数据处理, 生成水泵系统的能耗差值, 将注水井系统的运行能耗和注水井系统的历史平均运行能耗进行数据处理, 生成注水井系统的能耗差值, 分别依据的公式如下:

$$
\left\{ \begin{array}{l}\overline{E}_1 = \frac{1}{n}\sum_{i = 1}^{n}E_i \\ C_1 = E_1 - \overline{E}_1 \end{array} \right.
$$

其中,  $\overline{E}_1$  为水处理系统的历史平均运行能耗,  $\mathrm{E}_{\mathrm{i}}$  为第i个样本的水处理系统的历史运行能耗值, n为样本数量,  $C_1$  为水处理系统的能耗差值,  $\overline{E}_2$  为水泵系统的历史平均运行能耗,  $\mathrm{E}_{\mathrm{j}}$  为第j个样本的水泵系统的历史运行能耗值,  $C_2$  为水泵系统的能耗差值,  $\overline{E}_3$  为注水井系统的

历史平均运行能耗， $\mathrm{E}_z$  为第z个样本的注水井系统的历史运行能耗值。

9. 根据权利要求8所述的油田注水系统能耗优化方法，其特征在于：比较水处理系统的能耗差值、水泵系统的能耗差值和注水井系统的能耗差值大小，根据差值大小的顺序，确定油田注水系统优化的顺序的过程如下：

当  $\mathrm{C_1 > C_2 > C_3}$  ，首先优化水处理系统，然后优化水泵系统，最后优化注水井系统；当  $\mathrm{C_1 > C_3 > C_2}$  ，首先优化水处理系统，然后优化注水井系统，最后优化水泵系统；当  $\mathrm{C_2 > C_1 > C_3}$  ，首先优化水泵系统，然后优化水处理系统，最后优化注水井系统；当  $\mathrm{C_2 > C_3 > C_1}$  ，首先优化水泵系统，然后优化注水井系统，最后优化水处理系统；当  $\mathrm{C_3 > C_1 > C_2}$  ，首先优化注水井系统，然后优化水处理系统，最后优化水泵系统；当  $\mathrm{C_3 > C_2 > C_1}$  ，首先优化注水井系统，然后优化水泵系统，最后优化水处理系统。

10. 一种油田注水系统能耗优化装置，所述装置用于执行权利要求1-9任一所述的油田注水系统能耗优化方法，其特征在于，包括：

数据采集模块，用于采集油田注水系统中的水处理系统数据、水泵系统数据和注水井系统数据，其中，所述水处理系统数据包括水质参数和环境参数，所述水质参数包括pH值和悬浮物含量，所述环境参数包括温度和湿度，所述水泵系统数据包括管道压力、流体流速、管道的直径和运行时间，所述注水井系统数据包括井口压力参数和地层参数，所述井口压力参数包括注水井口压力、注水井的注水量和油井口压力，所述地层参数包括地层渗透率和孔隙度；

第一数据处理模块，用于将水质参数和环境参数进行数据处理和分析，生成水处理系统系数，将水处理系统系数和水处理系统的运行能耗建立线性回归方程，依据水处理系统系数，获取水处理系统的运行能耗；

第二数据处理模块，用于将水泵系统数据进行数据处理和分析，生成水泵系统压力系数，依据水泵系统压力系数，生成水泵系统的运行能耗；

第三数据处理模块，用于将注水井系统数据进行数据处理和分析，生成注水井系统压力系数，依据注水井系统压力系数，生成注水井系统的运行能耗；

数据分析模块，用于将水处理系统的运行能耗和水处理系统的历史平均运行能耗进行数据处理，生成水处理系统的能耗差值，将水泵系统的运行能耗和水泵系统的运行能耗的历史平均运行能耗进行数据处理，生成水泵系统的能耗差值，将注水井系统的运行能耗和注水井系统的历史平均运行能耗进行数据处理，生成注水井系统的能耗差值；

执行模块，用于比较水处理系统的能耗差值、水泵系统的能耗差值和注水井系统的能耗差值大小，根据差值大小的顺序，确定油田注水系统优化的顺序。

# 一种油田注水系统能耗优化方法及装置

# 技术领域

[0001] 本发明涉及油田注水系统能耗优化技术领域，具体为一种油田注水系统能耗优化方法及装置。

# 背景技术

[0002] 目前，油田注水是油田开发过程中向地层补充能量、提高采收率的重要手段之一。通过油田注水系统可以把质量合乎要求的水从注水井注入油层，以保持油层压力，实现油田注水。其中，油田注水系统的能耗巨大，占油田开发过程中总能耗的  $40\%$  以上。所以，油田注水系统的能耗优化对降低油田开发过程中的总能耗具有重要意义。

[0003] 现有技术中的，公开号为CN113700458B公开了一种油田注水系统的能耗优化方法及装置，属于油田领域。该方法包括：对待优化的第一油田注水系统中包括的多个第一注水节点进行简化处理，得到多个第二注水节点；确定多个注水管道组成的多个注水基环；确定由多个注水基环组成的第二油田注水系统；以多个第二注水节点中包括的每个注水泵的运行参数为变量，以第二油田注水系统的总能耗最低为目标，建立第二油田注水系统的能耗优化模型；根据注水管道中的流量，构建第二油田注水系统的能量平衡模型，通过能量平衡模型对第二油田注水系统进行模拟仿真，确定满足能耗优化模型的每个注水泵的运行参数；由于降低了能量平衡模型的维度，所以提高了油田注水系统能耗优化的效率和准确性。[0004] 但是还存在如下不足：由上述陈述可知，即在优化时，一方面存在数据不全面和计算不准确的问题，另一方面没有检测油田注水系统中具体设备或系统的能耗需要优化的问题，导致优化工作缺乏针对性，无法最有效和最快速地降低能耗。

[0005] 在所述背景技术部分公开的上述信息仅用于加强对本公开的背景的理解，因此它可以包括不构成对本领域普通技术人员已知的现有技术的信息。

# 发明内容

[0006] 本发明的目的在于提供一种油田注水系统能耗优化方法及装置，以解决上述背景技术中提出的问题。

[0007] 为实现上述目的，本发明提供如下技术方案：

[0008] 一种基于视频图像的煤矿监管方法及系统，具体步骤包括：

[0009] S1. 采集油田注水系统中的水处理系统数据、水泵系统数据和注水井系统数据，其中，所述水处理系统数据包括水质参数和环境参数，所述水质参数包括pH值和悬浮物含量，所述环境参数包括温度和湿度，所述水泵系统数据包括管道压力、流体流速、管道的直径和运行时间，所述注水井系统数据包括井口压力参数和地层参数，所述井口压力参数包括注水井口压力、注水井的注水量和油井口压力，所述地层参数包括地层渗透率和孔隙度；

[0010] S2. 将水质参数和环境参数进行数据处理和分析，生成水处理系统系数，将水处理系统系数和水处理系统的运行能耗建立线性回归方程，依据水处理系统系数，获取水处理系统的运行能耗；

[0011] S3. 将水泵系统数据进行数据处理和分析, 生成水泵系统压力系数, 依据水泵系统压力系数, 生成水泵系统的运行能耗;

[0012] S4. 将注水井系统数据进行数据处理和分析, 生成注水井系统压力系数, 依据注水井系统压力系数, 生成注水井系统的运行能耗;

[0013] S5. 将水处理系统的运行能耗和水处理系统的历史平均运行能耗进行数据处理, 生成水处理系统的能耗差值, 将水泵系统的运行能耗和水泵系统的运行能耗的历史平均运行能耗进行数据处理, 生成水泵系统的能耗差值, 将注水井系统的运行能耗和注水井系统的历史平均运行能耗进行数据处理, 生成注水井系统的能耗差值;

[0014] S6. 比较水处理系统的能耗差值、水泵系统的能耗差值和注水井系统的能耗差值大小, 根据差值大小的顺序, 确定油田注水系统优化的顺序。

[0015] 进一步地, 将水质参数和环境参数进行数据处理和分析, 生成水处理系统系数, 依据的公式如下:

$$
S = \frac{pH \times T}{H} + \sqrt{\frac{C}{pH \times H}} \tag{0016}
$$

[0017] 其中, S为水处理系统系数, 表示水质参数和环境参数的综合影响, pH为水的pH值, 指水中氢离子浓度的负对数, T为水处理系统的环境温度, H为水处理系统的环境湿度, C为水中的悬浮固体颗粒的含量。

[0018] 进一步地, 将水处理系统系数和水处理系统的运行能耗建立线性回归方程, 依据水处理系统系数, 获取水处理系统的运行能耗的过程如下:

[0019]  $\mathrm{E}_1 = \beta_0 \cdot \beta_1 \cdot \mathrm{S} + \mathrm{o}$

[0020] 其中,  $\mathrm{E}_1$  为水处理系统的运行能耗, S表示水处理系统系数,  $\beta_0$  为截距项,  $\beta_1$  为线性回归模型的系数,  $\mathrm{o}$  为误差项。

[0021] 进一步地, 将水泵系统数据进行数据处理和分析, 生成水泵系统压力系数, 依据的公式如下:

$$
P = \frac{\sum_{i = 1}^{n}(P_d \cdot V \cdot e^{-\alpha_i \cdot D} \cdot YS)}{\sqrt[n]{\beta_1}} \times \gamma \tag{0022}
$$

[0023] 其中, P为水泵系统压力系数,  $P_d$  为管道压力, V为流体流速, D为管道直径, YS为运行时间,  $\alpha_i$  为第i个样本的管道直径的系数,  $\beta_1$  为压力系数的数量级,  $\gamma$  为常数项, n为4。

[0024] 进一步地, 依据水泵系统压力系数, 生成水泵系统的运行能耗, 依据的公式如下:

[0025]  $\mathrm{E}_2 = \mathrm{k} \cdot \mathrm{P}$

[0026] 其中,  $\mathrm{E}_2$  为水泵系统的运行能耗, k为比例系数, 表示水泵系统压力系数P对水泵系统的运行能耗  $\mathrm{E}_2$  的影响程度。

[0027] 进一步地, 将注水井系统数据进行数据处理和分析, 生成注水井系统压力系数, 依据的公式如下:

$$
Z = \frac{\sum_{i = 1}^{m}\left(\frac{P_w \cdot Q_w}{P_o} \cdot \frac{r_i^2 \cdot \phi}{\sqrt{r_i} + \sqrt{\phi}}\right)}{\sqrt[m]{\beta_1}} \tag{0028}
$$

[0029] 其中，Z为注水井系统压力系数，  $\mathrm{P}_{\mathrm{w}}$  为注水井口压力，  $\mathrm{Q}_{\mathrm{w}}$  为注水井的注水量，  $\mathrm{P}_{\mathrm{o}}$  为油井口压力，  $\mathrm{r}_{\mathrm{i}}$  为第i个样本地层渗透率，  $\Phi$  为地层孔隙度，  $\beta_{2}$  为注水井系统压力系数的数量级，m为5。

[0030] 进一步地，依据注水井系统压力系数，生成注水井系统的运行能耗，依据的公式如下：

[0031]  $E_{3} = \frac{1}{Z^{2} + \mu}$

[0032] 其中，  $\mathrm{E}_{3}$  为注水井系统的运行能耗，  $\mu$  为常数。

[0033] 进一步地，将水处理系统的运行能耗和水处理系统的历史平均运行能耗进行数据处理，生成水处理系统的能耗差值，将水泵系统的运行能耗和水泵系统的运行能耗的历史平均运行能耗进行数据处理，生成水泵系统的能耗差值，将注水井系统的运行能耗和注水井系统的历史平均运行能耗进行数据处理，生成注水井系统的能耗差值，分别依据的公式如下：

[0034]  $\left\{ \begin{array}{l}\overline{E}_{1} = \frac{1}{n}\sum_{i = 1}^{n}E_{i} \\ C_{1} = E_{1} - \overline{E}_{1} \end{array} \right.$  [0035]

$$
\left\{ \begin{array}{l}\overline{E}_{1} = \frac{1}{n}\sum_{i = 1}^{n}E_{i} \\ C_{1} = E_{1} - \overline{E}_{1} \end{array} \right.
$$

$$
\left\{ \begin{array}{l}\overline{E}_{3} = \frac{1}{n}\sum_{z = 1}^{n}E_{z} \\ C_{3} = E_{3} - \overline{E}_{3} \end{array} \right.
$$

[0036] 其中，  $\overline{E}_{1}$  为水处理系统的历史平均运行能耗，  $\mathrm{E}_{1}$  为第i个样本的水处理系统的历史运行能耗值，n为样本数量，  $C_{1}$  为水处理系统的能耗差值，  $\overline{E}_{2}$  为水泵系统的历史平均运行能耗，  $\mathrm{E}_{j}$  为第j个样本的水泵系统的历史运行能耗值，  $C_{2}$  为水泵系统的能耗差值，  $\overline{E}_{3}$  为注水井系统的历史平均运行能耗，  $\mathrm{E}_{z}$  为第z个样本的注水井系统的历史运行能耗值。

[0037] 进一步地，比较水处理系统的能耗差值、水泵系统的能耗差值和注水井系统的能耗差值大小，根据差值大小的顺序，确定油田注水系统优化的顺序的过程如下：

[0038] 当  $C_{1} > C_{2} > C_{3}$ ，首先优化水处理系统，然后优化水泵系统，最后优化注水井系统；[0039] 当  $C_{1} > C_{3} > C_{2}$ ，首先优化水处理系统，然后优化注水井系统，最后优化水泵系统；[0040] 当  $C_{2} > C_{1} > C_{3}$ ，首先优化水泵系统，然后优化水处理系统，最后优化注水井系统；[0041] 当  $C_{2} > C_{3} > C_{1}$ ，首先优化水泵系统，然后优化注水井系统，最后优化水处理系统；[0042] 当  $C_{3} > C_{1} > C_{2}$ ，首先优化注水井系统，然后优化水处理系统，最后优化水泵系统；[0043] 当  $C_{3} > C_{2} > C_{1}$ ，首先优化注水井系统，然后优化水泵系统，最后优化水处理系统。[0044] 一种油田注水系统能耗优化装置，所述装置用于执行上述任一所述的油田注水系统能耗优化方法，包括：

[0045] 数据采集模块，用于采集油田注水系统中的水处理系统数据、水泵系统数据和注

水井系统数据，其中，所述水处理系统数据包括水质参数和环境参数，所述水质参数包括pH值和悬浮物含量，所述环境参数包括温度和湿度，所述水泵系统数据包括管道压力、流体流速、管道的直径和运行时间，所述注水井系统数据包括井口压力参数和地层参数，所述井口压力参数包括注水井口压力、注水井的注水量和油井口压力，所述地层参数包括地层渗透率和孔隙度；

[0046] 第一数据处理模块，用于将水质参数和环境参数进行数据处理和分析，生成水处理系统系数，将水处理系统系数和水处理系统的运行能耗建立线性回归方程，依据水处理系统系数，获取水处理系统的运行能耗；

[0047] 第二数据处理模块，用于将水泵系统数据进行数据处理和分析，生成水泵系统压力系数，依据水泵系统压力系数，生成水泵系统的运行能耗；

[0048] 第三数据处理模块，用于将注水井系统数据进行数据处理和分析，生成注水井系统压力系数，依据注水井系统压力系数，生成注水井系统的运行能耗；

[0049] 数据分析模块，用于将水处理系统的运行能耗和水处理系统的历史平均运行能耗进行数据处理，生成水处理系统的能耗差值，将水泵系统的运行能耗和水泵系统的运行能耗的历史平均运行能耗进行数据处理，生成水泵系统的能耗差值，将注水井系统的运行能耗和注水井系统的历史平均运行能耗进行数据处理，生成注水井系统的能耗差值；

[0050] 执行模块，用于比较水处理系统的能耗差值、水泵系统的能耗差值和注水井系统的能耗差值大小，根据差值大小的顺序，确定油田注水系统优化的顺序。

[0051] 与现有技术相比，本发明的有益效果是：

[0052] 本发明通过采集油田注水系统中的水处理系统数据、水泵系统数据和注水井系统数据，获取水处理系统的运行能耗、水泵系统的运行能耗和注水井系统的运行能耗，将水处理系统的运行能耗和水处理系统的历史平均运行能耗进行数据处理，生成水处理系统的能耗差值，将水泵系统的运行能耗和水泵系统的运行能耗的历史平均运行能耗进行数据处理，生成水泵系统的能耗差值，将注水井系统的运行能耗和注水井系统的历史平均运行能耗进行数据处理，生成注水井系统的能耗差值，比较水处理系统的能耗差值、水泵系统的能耗差值和注水井系统的能耗差值大小，根据差值大小的顺序，确定油田注水系统优化的顺序。因此，通过采集油田注水系统中的水处理系统数据、水泵系统数据和注水井系统数据，可以全面和准确评估油田注水系统能耗，还可以确定优化的优先顺序，继而优先对能耗较大的系统优化，使得优化工作更加有针对性和高效。

# 附图说明

[0053] 图1为本发明整体方法流程示意图；[0054] 图2为本发明模块组成框图。

# 具体实施方式

[0055] 为使本发明的目的、技术方案和优点更加清楚明白，以下结合具体实施例，对本发明进一步详细说明。

[0056] 需要说明的是，除非另外定义，本发明使用的技术术语或者科学术语应当为本发明所属领域内具有一般技能的人士所理解的通常意义。本发明中使用的“第一”“第二”以及

类似的词语并不表示任何顺序、数量或者重要性，而只是用来区分不同的组成部分。“包括”或者“包含”等类似的词语意指出现该词前面的元件或者物件涵盖出现在该词后面列举的元件或者物件及其等同，而不排除其他元件或者物件。“连接”或者“相连”等类似的词语并非限定于物理的或者机械的连接，而是可以包括电性的连接，不管是直接的还是间接的。“上”“下”“左”“右”等仅用于表示相对位置关系，当被描述对象的绝对位置改变后，则该相对位置关系也可能相应地改变。

[0057] 实施例：

[0058] 请参阅图1，本发明提供一种技术方案：

[0059] 一种油田注水系统能耗优化方法，具体步骤包括：

[0060] S1. 采集油田注水系统中的水处理系统数据、水泵系统数据和注水井系统数据，其中，所述水处理系统数据包括水质参数和环境参数，所述水质参数包括pH值和悬浮物含量，所述环境参数包括温度和湿度，所述水泵系统数据包括管道压力、流体流速、管道的直径和运行时间，所述注水井系统数据包括井口压力参数和地层参数，所述井口压力参数包括注水井口压力、注水井的注水量和油井口压力，所述地层参数包括地层渗透率和孔隙度；

[0061] S2. 将水质参数和环境参数进行数据处理和分析，生成水处理系统系数，将水处理系统系数和水处理系统的运行能耗建立线性回归方程，依据水处理系统系数，获取水处理系统的运行能耗；

[0062] S3. 将水泵系统数据进行数据处理和分析，生成水泵系统压力系数，依据水泵系统压力系数，生成水泵系统的运行能耗；

[0063] S4. 将注水井系统数据进行数据处理和分析，生成注水井系统压力系数，依据注水井系统压力系数，生成注水井系统的运行能耗；

[0064] S5. 将水处理系统的运行能耗和水处理系统的历史平均运行能耗进行数据处理，生成水处理系统的能耗差值，将水泵系统的运行能耗和水泵系统的运行能耗的历史平均运行能耗进行数据处理，生成水泵系统的能耗差值，将注水井系统的运行能耗和注水井系统的历史平均运行能耗进行数据处理，生成注水井系统的能耗差值；

[0065] S6. 比较水处理系统的能耗差值、水泵系统的能耗差值和注水井系统的能耗差值大小，根据差值大小的顺序，确定油田注水系统优化的顺序。

[0066] 当油田注水系统的水处理系统数据变化时，油田注水系统的能耗就会变化，以下为具体原因：

[0067] pH值变化：pH值是衡量水溶液酸碱性的指标，它会影响到水处理系统中各种化学反应的进行，当原始水源的pH值发生变化时，水处理系统需要调整化学品投加量和处理工艺，以确保水质达标，例如，如果水源的pH值偏低，可能需要增加碱性化学品来调节水质，这会增加化学品的使用量和处理成本，从而增加了能耗。

[0068] 悬浮物含量变化：悬浮物含量是衡量水中固体颗粒或悬浮物的浓度，当原始水源中的悬浮物含量发生变化时，水处理系统需要调整过滤和沉淀工艺，以清除悬浮物并提高水质，增加悬浮物含量可能需要增加过滤次数或加大过滤压力，以确保水质达标，这会增加系统的能耗。

[0069] 因此，水处理系统数据的变化，特别是pH值和悬浮物含量的变化，会直接影响到水处理系统的能耗，系统需要根据水质变化调整处理工艺和化学品投加量，以满足水质标准，

这可能会增加能耗。

[0070] 另外，水处理系统的运行状态受环境因素的影响，例如温度和湿度，当环境参数发生变化时，系统可能需要调整运行参数或增加能耗以应对新的环境条件，例如增加设备的冷却负荷、加大风机的功率。

[0071] 综上所述，油田注水系统的水处理系统数据变化会直接影响系统的能耗，主要是因为水质变化和环境参数变化引起的。

[0072] 当油田注水系统的水泵系统数据变化时，油田注水系统的能耗就会变化，以下为具体原因：

[0073] 管道压力变化：水泵系统负责输送水流至注水井，当管道压力发生变化时，水泵需要调整输出压力以适应新的工况，增加管道压力通常需要水泵提供更大的功率来克服阻力，从而增加了能耗。

[0074] 流体流速变化：流体流速是水泵系统的一个重要参数，它决定了输送流体的速度和能耗，当流体流速发生变化时，水泵可能需要调整转速或输出功率以满足新的需求，这会影响系统的能耗。

[0075] 管道直径变化：管道直径的变化会影响水流的速度和阻力，进而影响到水泵系统的工作状态和能耗。较小直径的管道通常会增加流体的阻力，从而需要水泵提供更大的功率来维持一定的流量，增加了能耗。

[0076] 运行时间变化：水泵系统的运行时间也会影响能耗，当需要输送更多的水量或增加运行时间时，水泵系统将消耗更多的能量，从而增加系统的能耗。

[0077] 综上所述，水泵系统数据的变化会直接影响到油田注水系统的能耗，主要是因为管道压力、流体流速、管道直径以及运行时间的变化，从而影响了水泵系统的工作状态和能源消耗情况。

[0078] 当油田注水系统的注水井系统数据变化时，油田注水系统的能耗就会变化，以下为具体原因：

[0079] 井口压力变化：注水井口压力是指在油井口或井口处的压力，当注水井口压力发生变化时，会影响到注水井系统的工作状态和能耗，例如，如果注水井口压力增加，可能需要水泵提供更大的功率来克服压力差，从而增加了能耗。

[0080] 注水量变化：注水量是指通过注水井注入地下的水量，当注水量发生变化时，会直接影响到水泵系统的工作负荷和能耗，增加注水量通常需要水泵提供更大的流量和功率，从而增加了系统的能耗。

[0081] 油井口压力变化：油井口压力是指油井产出口的压力，当油井口压力发生变化时，会影响到注水井系统的注水量和运行状态，如果油井口压力降低，可能需要增加注水量来维持油田的产量，这会增加系统的能耗。

[0082] 地层参数变化：地层参数包括地层渗透率和孔隙度，当地层参数发生变化时，会影响到注水井的注水效果和需求量，例如，如果地层渗透率降低，可能需要增加注水量来提高地层的渗透性，从而增加了系统的能耗。

[0083] 综上所述，注水井系统数据的变化会直接影响到油田注水系统的能耗，主要是因为井口压力、注水量、油井口压力以及地层参数的变化，从而影响了注水井系统的工作状态和能源消耗情况。

[0084] 在上述实施例的基础上, 将水质参数和环境参数进行数据处理和分析, 生成水处理系统系数, 依据的公式如下:

$$
S = \frac{pH \times T}{H} + \sqrt{\frac{C}{pH \times H}} \tag{0085}
$$

[0086] 其中, S代表水处理系统系数, 表示水质参数和环境参数的综合影响, pH为水的pH值, 是指水中氢离子浓度的负对数, 提高pH值会增加水的碱性, 降低pH值会增加水的酸性, 当pH值增加时, 系数S也会相应增加, 表示水质对系统性能的影响增强, T为温度, 表示水处理系统的环境温度, 温度的升高会导致水中溶解气体减少, 化学反应速率增加, 从而影响水处理系统的性能, 当温度T增加时, 系数S会相应增加, 表示温度对系统性能的影响增强, H为湿度, 表示水处理系统的环境湿度, 湿度的增加会影响水中溶解气体的含量和化学反应速率, 从而影响系统的性能, 当湿度H增加时, 系数S会相应减小, 表示湿度对系统性能的影响减弱, C为悬浮物含量, 表示水中的悬浮固体颗粒的含量, 悬浮物含量的增加会增加水的浑浊度, 影响水的处理效果, 当悬浮物含量C增加时, 系数S会相应增加, 表示悬浮物对系统性能的影响增强。

[0087] 这个公式的值域为S≥0, 即系数S的取值范围为非负实数集合, 当系数(S)较小时, 表示水质参数和环境参数对系统的影响较小; 当系数S较大时, 表示水质参数和环境参数对系统的影响较大, 通过对水质参数和环境参数的综合考虑, 可以根据水处理系统系数S来评估系统的性能表现, 进而优化水处理方案和系统设计。

[0088] 上述的公式包含了水处理系统能耗的主要影响因素, 影响因素为水质参数(pH值和悬浮物含量)和环境参数(温度和湿度), 这些参数都是影响水处理系统能耗的重要因素, 因此需要在公式中进行考虑;

[0089] 水质参数影响:

[0090] pH值: pH值反映了水的酸碱性, 对水质有重要影响, 在上述公式中, pH与其他参数相乘并除以H, 表示了pH值对系统系数S的影响, pH值增加, 系统系数S可能会增加, 表示水的酸碱性越强, 对系统性能的影响可能更大。

[0091] 悬浮物含量: 悬浮物含量影响着水的浑浊度和处理难度, 在上述公式中, 悬浮物含量C与pH和H的乘积的比值的平方根被加到了(pH×T/H)之后, 表示了悬浮物含量对系统系数S的影响, 悬浮物含量越高, 系统系数S增加, 表示处理难度增加。

[0092] 环境参数影响:

[0093] 温度: 温度影响着水的化学反应速率和溶解性气体的含量, 在上述公式中, 温度T与pH相乘后除以湿度H, 表示了温度对系统系数S的影响, 温度增加, 系统系数S增加, 表示化学反应速率增加, 对系统性能的影响更大。

[0094] 湿度: 湿度影响着水的溶解气体含量和化学反应速率, 在上述公式中, 湿度H在分母位置, 表示了湿度对系统系数S的影响, 湿度减小, 系统系数S减小, 表示溶解气体含量减少, 对系统性能的影响减小。

[0095] 系统系数S的含义:

[0096] 系统系数S表示了水质参数和环境参数对水处理系统性能的综合影响, 当系统系数S较大时, 说明水质参数和环境参数对系统性能的影响较大, 处理难度也会增加; 当系统系数S较小时, 说明系统受影响较小, 处理相对容易。

[0097] 因此，这个公式能够较好地描述水质参数和环境参数对水处理系统性能的综合影响，提供了一种量化的方法来评估系统性能并优化处理方案。

[0098] 在上述实施例的基础上，将水处理系统系数和水处理系统的运行能耗建立线性回归方程，依据水处理系统系数，获取水处理系统的运行能耗的过程如下：

[0099]  $\mathrm{E}_1 = \beta_0 - \beta_1\bullet \mathrm{S} + \delta$

[0100] 其中， $\mathrm{E}_1$  为水处理系统的运行能耗，S表示水处理系统系数， $\beta_0$  为截距项， $\beta_1$  为线性回归模型的系数， $\delta$  为误差项。

[0101] 上述线性函数关系假设了水处理系统系数S和系统运行能耗  $\mathrm{E}_1$  之间存在着线性关系，即系统运行能耗随着系统系数的增加而线性减小。

[0102] 通过上述的公式，可以获知水处理系统的运行能耗和水处理系统系数之间的函数关系，现还可以通过表格表达水处理系统的运行能耗和水处理系统系数关系，以下为表格内容：

[0103]

<table><tr><td>水处理系统的运行能耗</td><td>水处理系统系数</td><td>截距项</td><td>线性回归模型系数</td><td>误差项</td></tr><tr><td>35.2</td><td>0.8</td><td>12.4</td><td>4.6</td><td>1.2</td></tr><tr><td>41.5</td><td>0.6</td><td>12.4</td><td>5.2</td><td>1.5</td></tr><tr><td>29.8</td><td>1</td><td>12.4</td><td>3.8</td><td>0.8</td></tr><tr><td>37.1</td><td>0.7</td><td>12.4</td><td>4.9</td><td>1.3</td></tr><tr><td>33.6</td><td>0.9</td><td>12.4</td><td>4.3</td><td>1</td></tr></table>

[0104] 通过上述的表格，可以获知，当水处理系统系数增加时，水处理系统的运行能耗会减小，因为水处理系统系数的增加通常会伴随着能效的提高，从而导致运行能耗的减少。

[0105] 在上述实施例的基础上，将水泵系统数据进行数据处理和分析，生成水泵系统压力系数，依据的公式如下：

$$
P = \frac{\sum_{i = 1}^{n}(P_d\cdot V\cdot e^{-\alpha_i\cdot D}\cdot YS)}{\sqrt[n]{\beta_1}}\times \gamma \tag{0106}
$$

[0107] 其中，P为水泵系统压力系数， $\mathrm{P}_d$  为管道压力，V为流体流速，D为管道直径，YS为运行时间， $\alpha$  为第i个样本的管道直径的系数， $\beta_1$  为压力系数的数量级， $\gamma$  为常数项，n为4。

[0108] 上述公式参数之间具有交互规则，例如，增加管道压力， $\mathrm{P}_d$  会导致水泵系统压力系数P的增加，因为更高的压力意味着更大的驱动力，另外，增加流体流速V会提高水泵系统压力系数P，因为更快的流速意味着更高的能量转移率，管道直径D的增加会降低水泵系统压力系数P，因为更大的管道直径会导致更大的摩擦损失，运行时间YS的增加会对水泵系统压力系数P产生影响，会随着运行时间YS的增加而增加。

[0109] 通过上述的公式，可以获知水泵系统压力系数和管道压力、流体流速、管道的直径和运行时间之间的函数关系，现还可以通过表格表达水泵系统系数和管道压力、流体流速、管道的直径和运行时间之间的函数关系，以下为表格内容：

[0110]

<table><tr><td>水泵系统压力系数</td><td>管道压力(Pa)</td><td>流体流速(m/s)</td><td>管道直径(m)</td><td>运行时间(s)</td></tr><tr><td>0.8</td><td>5000</td><td>2</td><td>0.3</td><td>3800</td></tr><tr><td>0.9</td><td>5500</td><td>2.5</td><td>0.4</td><td>4200</td></tr></table>

[0111]

<table><tr><td>0.7</td><td>4800</td><td>1.8</td><td>0.25</td><td>3600</td></tr><tr><td>0.85</td><td>5200</td><td>2.2</td><td>0.35</td><td>4200</td></tr><tr><td>0.75</td><td>4900</td><td>2.1</td><td>0.28</td><td>4000</td></tr></table>

[0112] 通过上述的表格，可以获知，当管道压力、流体流速、管道的直径和运行时间增加时，水泵系统压力系数会增加。

[0113] 在上述实施例的基础上，依据水泵系统压力系数，生成水泵系统的运行能耗，依据的公式如下：

[0114]  $\mathrm{E}_2 = \mathrm{k}\bullet \mathrm{P}$

[0115] 其中， $\mathrm{E}_2$  为水泵系统的运行能耗，k为比例系数，表示水泵系统压力系数P对水泵系统的运行能耗  $\mathrm{E}_2$  的影响程度。

[0116] 上述公式贴合了实际，因为运行能耗通常会受到水泵系统的压力系数的影响，通过调整k的值，根据实际情况对压力系数P的影响进行调节。

[0117] 在上述实施例的基础上，将注水井系统数据进行数据处理和分析，生成注水井系统压力系数，依据的公式如下：

$$
Z = \frac{\sum_{i = 1}^{m}\left(\frac{P_w\cdot Q_w}{P_o}\cdot\frac{r_i^2\cdot\phi}{\sqrt{r_i + \sqrt{\phi}}}\right)}{\sqrt[4]{\beta_1}} \tag{0118}
$$

[0119] 其中，Z为注水井系统压力系数， $\mathrm{P}_w$  为注水井口压力， $\mathrm{Q}_w$  为注水井的注水量， $\mathrm{P}_o$  为油井口压力， $\mathrm{r}_i$  为第i个样本地层渗透率， $\phi$  为地层孔隙度， $\beta_2$  为注水井系统压力系数的数量级，m为5。

[0120] 通过上述的公式，可以获知，当注水井口压力  $\mathrm{P}_w$  增加时，如果注水量  $\mathrm{Q}_w$  和油井口压力  $\mathrm{P}_o$  保持不变，压力系数Z也会随之增加，地层渗透率r和孔隙度  $\phi$  的增加会导致压力系数Z增加，但增长速度会受到平方根项的影响。

[0121] 当压力系数Z较小时，表示注水井系统的压力较低，可能需要增加注水量或增加地层渗透率；当压力系数Z较大时，表示注水井系统的压力较高，可能需要减少注水量或降低地层渗透率。

[0122] 在上述实施例的基础上，依据注水井系统压力系数，生成注水井系统的运行能耗，依据的公式如下：

[0123]  $E_{3} = \frac{1}{Z^{2} + \mu}$

[0124] 其中， $\mathrm{E}_3$  为注水井系统的运行能耗， $\mu$  为常数，用于调节能耗与压力系数之间的关系，较小的  $\mu$  值会导致能耗对压力系数的变化更为敏感，而较大的  $\mu$  值则会使能耗变化更为平缓。

[0125] 这个公式反映了注水井系统的运行能耗与压力系数之间的倒数关系，即当压力系

数越高时, 系统的运行能耗越低, 这是因为较高的压力系数通常意味着更有效的注水井系统运行, 从而降低了能耗。

[0126] 在上述实施例的基础上, 将水处理系统的运行能耗和水处理系统的历史平均运行能耗进行数据处理, 生成水处理系统的能耗差值, 将水泵系统的运行能耗和水泵系统的运行能耗的历史平均运行能耗进行数据处理, 生成水泵系统的能耗差值, 将注水井系统的运行能耗和注水井系统的历史平均运行能耗进行数据处理, 生成注水井系统的能耗差值, 分别依据的公式如下:

$$
\left\{ \begin{array}{l}\overline{E}_1 = \frac{1}{n}\sum_{i = 1}^{n}E_i \\ C_1 = E_1 - \overline{E}_1 \end{array} \right. \[ \left\{ \begin{array}{l}\overline{E}_2 = \frac{1}{n}\sum_{j = 1}^{n}E_j \\ C_2 = E_2 - \overline{E}_2 \end{array} \right. \] \[ \left\{ \begin{array}{l}\overline{E}_3 = \frac{1}{n}\sum_{z = 1}^{n}E_z \\ C_3 = E_3 - \overline{E}_3 \end{array} \right. \] \tag{[0127]}
$$

[0128] 其中,  $\overline{E}_1$  为水处理系统的历史平均运行能耗,  $\mathbb{E}_1$  为第i个样本的水处理系统的历史运行能耗值, n为样本数量,  $C_1$  为水处理系统的能耗差值,  $\overline{E}_2$  为水泵系统的历史平均运行能耗,  $\mathbb{E}_j$  为第j个样本的水泵系统的历史运行能耗值,  $C_2$  为水泵系统的能耗差值,  $\overline{E}_3$  为注水井系统的历史平均运行能耗,  $\mathbb{E}_z$  为第z个样本的注水井系统的历史运行能耗值。

[0129] 在上述实施例的基础上, 比较水处理系统的能耗差值、水泵系统的能耗差值和注水井系统的能耗差值大小, 根据差值大小的顺序, 确定油田注水系统优化的顺序的过程如下:

[0130] 当  $C_1 > C_2 > C_3$ , 首先优化水处理系统, 然后优化水泵系统, 最后优化注水井系统; [0131] 当  $C_1 > C_3 > C_2$ , 首先优化水处理系统, 然后优化注水井系统, 最后优化水泵系统; [0132] 当  $C_2 > C_1 > C_3$ , 首先优化水泵系统, 然后优化水处理系统, 最后优化注水井系统; [0133] 当  $C_2 > C_3 > C_1$ , 首先优化水泵系统, 然后优化注水井系统, 最后优化水处理系统; [0134] 当  $C_3 > C_1 > C_2$ , 首先优化注水井系统, 然后优化水处理系统, 最后优化水泵系统; [0135] 当  $C_3 > C_2 > C_1$ , 首先优化注水井系统, 然后优化水泵系统, 最后优化水处理系统。

[0136] 公式中的α和β的具体取值一般由本领域技术人员根据实际情况来确定, 该公式本质为加权求和进行综合分析, 由本领域技术人员采集多组样本数据, 并对每一组样本数据设定对应的预设比例系数, 将设定的预设比例系数和采集的样本数据代入公式, 通过反复试验和参数调整, 观察模型输出的准确性和结果的合理性, 逐步调整这些因子系数, 并对比不同参数设置下模型的性能和效果, 找到最优的系数组合, 将计算得到的因子系数进行筛选并取均值, 得到α和β的取值。

[0137] 另外, 预设因子系数的大小是为了将各个参数进行量化得到的一个具体的数值, 其为了便于后续比较, 关于系数的大小, 取决于样本数据的多少及本领域技术人员对每一

组样本数据初步设定对应的预设比例系数，并不唯一，只要不影响参数与量化后数值的比例关系即可。

[0138] 请参阅图2，本发明还提供一种技术方案：

[0139] 一种油田注水系统能耗优化装置，所述装置用于执行上述任一所述的油田注水系统能耗优化方法，包括：

[0140] 数据采集模块，用于采集油田注水系统中的水处理系统数据、水泵系统数据和注水井系统数据，其中，所述水处理系统数据包括水质参数和环境参数，所述水质参数包括pH值和悬浮物含量，所述环境参数包括温度和湿度，所述水泵系统数据包括管道压力、流体流速、管道的直径和运行时间，所述注水井系统数据包括井口压力参数和地层参数，所述井口压力参数包括注水井口压力、注水井的注水量和油井口压力，所述地层参数包括地层渗透率和孔隙度；

[0141] 第一数据处理模块，用于将水质参数和环境参数进行数据处理和分析，生成水处理系统系数，将水处理系统系数和水处理系统的运行能耗建立线性回归方程，依据水处理系统系数，获取水处理系统的运行能耗；

[0142] 第二数据处理模块，用于将水泵系统数据进行数据处理和分析，生成水泵系统压力系数，依据水泵系统压力系数，生成水泵系统的运行能耗；

[0143] 第三数据处理模块，用于将注水井系统数据进行数据处理和分析，生成注水井系统压力系数，依据注水井系统压力系数，生成注水井系统的运行能耗；

[0144] 数据分析模块，用于将水处理系统的运行能耗和水处理系统的历史平均运行能耗进行数据处理，生成水处理系统的能耗差值，将水泵系统的运行能耗和水泵系统的运行能耗的历史平均运行能耗进行数据处理，生成水泵系统的能耗差值，将注水井系统的运行能耗和注水井系统的历史平均运行能耗进行数据处理，生成注水井系统的能耗差值；

[0145] 执行模块，用于比较水处理系统的能耗差值、水泵系统的能耗差值和注水井系统的能耗差值大小，根据差值大小的顺序，确定油田注水系统优化的顺序。

[0146] 上述公式均是去量纲取其数值计算，公式是由采集大量数据进行软件模拟得到最近真实情况的一个公式，公式中的预设参数由本领域的技术人员根据实际情况进行设置。

[0147] 上述实施例，可以全部或部分地通过软件、硬件、固件或其他任意组合来实现。当使用软件实现时，上述实施例可以全部或部分地以计算机程序产品的形式实现。本领域技术人员可以意识到，结合本文中所公开的实施例描述的各示例的单元及算法步骤，能够通过电子硬件，或者计算机软件和电子硬件的结合来实现。这些功能究竟以硬件还是软件方法来执行，取决于技术方案的特定应用和设计约束条件。

[0148] 所述作为分离部件说明的单元可以是或者也可以不是物理上分开的，作为单元显示的部件可以是或者也可以不是物理单元，既可以位于一个地方，或者也可以分布到多个网络单元上。可以根据实际的需要选择其中的部分或者全部单元来实现本实施例方案的目的。

[0149] 以上所述，仅为本申请的具体实施方式，但本申请的保护范围并不局限于此，任何熟悉本技术领域的技术人员在本申请揭露的技术范围内，可轻易想到变化或替换，都应涵盖在本申请的保护范围之内。

步骤S1：采集油田注水系统中的水处理系统数据、水泵系统数据和注水井系统数据，其中，所述水处理系统数据包括水质参数和环境参数，所述水质参数包括pH值和悬浮物含量，所述环境参数包括温度和湿度，所述水泵系统数据包括管道压力、流体流速、管道的直径和运行时间，所述注水井系统数据包括井口压力参数和地层参数，所述井口压力参数包括注水井口压力、注水井的注水量和油井口压力，所述地层参数包括地层渗透率和孔隙度

![](images/e9bdcb3343706f8cf34be2fdde78c9d9a1efb0e18589dfe045fb209574e41c00.jpg)  
图1

![](images/bbd2d72dccd6d861a498bb6172cc5d425f9af961dae6005e9f7de0e02e55bc86.jpg)  
图2