# 说明书摘要

说明书摘要本发明公开了一种基于PLC的低浓度煤层气抑爆式提浓控制方法，是应用于低浓度煤层气抑爆式提浓的控制系统中，该控制系统包括：PLC控制器、压缩机、总阀门、若干个阀门、变压吸附系统、若干个继电器、报警模块、压力变送器、温度变送器、浓度变送器；该方法是利用PLC控制器对设备中缓冲罐和吸附塔的温度变送器、压力变送器和浓度变送器的数值分析从而对若干个阀门、压缩机的功率以及报警模块进行控制。本发明能实时监测煤层气浓度，并对提浓过程中的控检测数据和调节参数进行实时优化与控制，在出现爆炸情况时及时报警，从而确保整个工艺流程处于安全、高效运行状态。

# 权利要求书

1、一种基于PLC的低浓度煤层气抑爆式提浓控制方法，其特征在于，是应用于低浓度煤层气抑爆式提浓的控制系统中，所述控制系统包括：PLC控制器、压缩机(5)、总阀门(001)、若干个阀门、变压吸附系统、若干个继电器、报警模块、压力变送器、温度变送器、浓度变送器；

所述变压吸附系统包括：原料罐(1)、缓冲罐(2)、六个吸附塔、废气罐(3)、产品罐(4)；所述原料罐(1)和所述压缩机(5)之间设置有总阀门(001)并通过导气管连接；所述压缩机

(5)和缓冲罐(2)之间通过导气管连接；

所述六个吸附塔是由A塔、B塔、C塔、D塔、E塔和F塔组成；

六个吸附塔分别通过六个进气管与所述缓冲罐(2)连通，并在每个进气管上设置有对应的进气口阀门；

六个吸附塔分别通过六个废气管与所述废气罐(3)连通，并在每个废气管上设置有对应的废气罐出气口阀门；

六个吸附塔分别通过六个出气管与所述产品罐(4)连通，并在每个出气管上设置有对应的产品气出气口阀门；

六个吸附塔之间分别通过一个流通管互相连通，并在六个吸附塔所对应的流通管上设置有并列的2个流通阀门；

所述压力变送器分别放置在所述缓冲罐(2)和六个吸附塔中；

所述浓度变送器分别放置在所述缓冲罐(2)、废气罐(3)和所述产品罐(4)中；

所述温度变送器分别放置在六个吸附塔的外侧；

所述低浓度煤层气抑爆式提浓控制方法是按如下步骤进行：

步骤1、PLC控制器发送开车信号给所述变压吸附系统，所述变压吸附系统根据所述开车信号打开总阀门(001)，使得所述压缩机(5)工作并开始对所述原料罐(1)中的气体进行升压，

从而将升压后的气体送入所述缓冲罐(2)；

步骤2、所述PLC控制器利用压强传感器采集所述缓冲罐(2)的压力值β，并判断所述压力值β是否达到指定范围；若达到，则执行步骤3；否则，执行步骤9；

步骤3、所述PLC控制器控制压缩机(5)的当前功率保持不变，并打开A塔的进气口阀门(a4)用于对A塔进行充气；

步骤4、记录A塔的进气管上的进气口阀门(a4)开启时间点  $\mathrm{T_{A0}}$  以及A塔的进气时间 $\mathrm{T_A}$  ，从而得到气体到达A塔所需的时间  $\mathrm{t_A = T_A - T_{A0}}$  ，并计算A塔对吸附质的吸附时间  $\mathrm{t_{A0} = (t_A}$ $+\mathrm{t}) - (\frac{1 - \epsilon}{V_{As}})\mathrm{z}$  ；其中，表示吸附剂颗粒之间的轴向距离，表示A塔中吸附剂的地层孔隙度，  $\nu_{\mathrm{As}}$  表示A塔中气体流动速率，  $\mathrm{t_A}$  示气体到达A塔的时间，所述进气时间  $\mathrm{T_A}$  是指利用压强传感器检测A塔的压强信号P(A)的时间点；

步骤5、所述PLC控制器判断A塔的压强信号P(A)是否达到最佳吸附压强α；若达到，则执行步骤6；否则，增加压缩机(5)的当前功率并继续对A塔进行充气；并执行步骤4；

步骤6、所述PLC控制器判断A塔的吸附时间  $\mathrm{t_{A0}}$  是否达到设定时间  $\mathrm{t_{max}}$  ，若达到，则执行步骤7；否则，判断在吸附时间  $\mathrm{t_{A0}}$  内，压强信号P(A)的变化波动范围是否超过阈值，若是，则表示A塔的出气管上的产品气出气口阀门(a1)或者A塔的气密性不足，并执行步骤34；否则，则执行步骤7；

步骤7、所述PLC控制器控制所述继电器打开所述废气罐(3)出气口阀门(a5)，并利用所述废气罐(3)的浓度变送器对A塔排出气体进行检测，并执行步骤8；

步骤8、判断所述废气罐(3)A塔排出气体浓度是否在设定的吸附塔浓度变化范围内；若是，则关闭所述废气罐(3)出气口阀门(a5)并执行步骤10；否则，关闭阀门废气罐(3)出气口阀门(a5)，并将吸附时间  $\mathbf{t}_{\mathrm{A0}}$  加上  $\delta_{\mathrm{xt_{A0}}}$  后，返回步骤6顺序执行；  $\delta$  表示0—1之间的倍数；

步骤9、判断在所述压缩机(5)开始工作时间  $\Delta t$  内所述缓冲罐(2)内的压强是否达到设定值，若达到，则执行步骤3；否则，将调大压缩机(5)的工作功率，执行步骤3；

步骤10、六个吸附塔进行A塔吸附循环工作：

所述PLC控制器控制A塔的进气口阀门(a4)打开，使得A塔进气，同时控制A塔的产品气出气口阀门(a1)打开，使得A塔出产品气；

所述PLC控制器分别控制F塔的流通阀门(f3)和B塔的流通阀门(b3)打开，用于给B塔充压；

所述PLC控制器分别控制E塔的流通阀门(e2)和C塔的流通阀门(c2)打开，用于给C塔充压；

所述PLC控制器控制D塔的废气罐出气口阀门(d5)打开，使得D塔排废气；

所述PLC控制器分别控制D塔的废气罐出气口阀门(d5)、E塔的流通阀门(e2)、C塔的

流通阀门(c2)、F塔的流通阀门(f3)、B塔的流通阀门(b3)、A塔的产品气出气口阀门(a1)、A塔的进气口阀门(a4)在设定时间值  $\lambda$  后全部关闭；

步骤11、判断步骤10中阀门在设定时间值  $\lambda$  后是否全部关闭，若关闭，则执行步骤

12；否则，执行步骤34；

步骤12、六个吸附塔进行B塔充压循环工作：

所述PLC控制器控制A塔的进气口阀门(a4)打开，使得A塔进气，同时控制A塔的产品气出气口阀门(a1)打开，使得A塔出产品气；

所述PLC控制器控制B塔的产品气出气口阀门(b1)打开，用于对B塔充压；

所述PLC控制器分别控制F塔的流通阀门(f3)和C塔的流通阀门(c3)打开，用于给C塔充压；

所述PLC控制器分别控制E塔的流通阀门(e2)和D塔的流通阀门(d2)打开，用于给D塔冲洗；

所述PLC控制器分别控制E塔的流通阀门(e2)、D塔的流通阀门(d2)、F塔的流通阀门(f3)、C塔的流通阀门(c3)、B塔的产品气出气口阀门(b1)、A塔的产品气出气口阀门(a1)

、A塔的进气口阀门(a4)在设定时间值  $\lambda$  后全部关闭；

步骤13、判断步骤12中阀门在设定时间值  $\lambda$  后是否全部关闭，若关闭，则执行步骤

14；否则，执行步骤34；

步骤14、六个吸附塔进行B塔吸附循环工作：

所述PLC控制器分别控制A塔的流通阀门(a2)和C塔的流通阀门(c2)打开，用于给C塔充压；

所述PLC控制器控制B塔的进气口阀门(b4)打开，使得B塔进气，同时控制B塔的产品气出气口阀门(b1)打开，使得B塔出产品气；

所述PLC控制器分别控制F塔的流通阀门(f3)和D塔的流通阀门(d3)打开，用于给D塔充压；

所述PLC控制器控制E塔的废气罐出气口阀门(e5)打开，使得E塔排废气；

所述PLC控制器分别控制E塔的废气罐出气口阀门(e5)、F塔的流通阀门(f3)、D塔的流通阀门(d3)、B塔的产品气出气口阀门(b1)、B塔的进气口阀门(b4)、A塔的流通阀门(a2)、C塔的流通阀门(c2)在设定时间值  $\lambda$  后全部关闭；

步骤15、判断步骤14中阀门在设定时间值  $\lambda$  后是否全部关闭，若关闭，则执行步骤

16；否则，执行步骤34；

步骤16、六个吸附塔进行C塔充压循环工作：

所述PLC控制器分别控制A塔的流通阀门(a3)和D塔的流通阀门(d3)打开，用于给D塔

充压；

所述PLC控制器控制B塔的进气口阀门(b4)打开，使得B塔进气，同时控制B塔的产品气出气口阀门(b1)打开，使得B塔出产品气；

所述PLC控制器控制C塔的产品气出气口阀门(c1)打开，用于给C塔充压；

所述PLC控制器分别控制F塔的流通阀门(f2)和E塔的流通阀门(e2)打开，用于给E塔冲洗；

所述PLC控制器分别控制F塔的流通阀门(f2)、E塔的流通阀门(e2)、C塔的产品气出气口阀门(c1)、B塔的产品气出气口阀门(b1)、B塔的进气口阀门(b4)、A塔的流通阀门(a3)、D塔的流通阀门(d3)在设定时间值  $\lambda$  后全部关闭；

步骤17、判断步骤16中阀门在设定时间值  $\lambda$  后是否全部关闭，若关闭，则执行步骤

18；否则，执行步骤34；

步骤18、六个吸附塔进行C塔吸附循环工作：

所述PLC控制器分别控制A塔的流通阀门(a3)和E塔的流通阀门(e3)打开，用于给E塔

充压；

所述PLC控制器分别控制B塔的流通阀门(b2)和D塔的流通阀门(d2)打开，用于给D塔

充压；

所述PLC控制器控制C塔的进气口阀门(c4)打开，使得C塔进气，同时控制C塔的产品气出气口阀门(c1)打开，使得C塔出产品气；

所述PLC控制器控制F塔的废气罐出气口阀门(f5)打开，用于F塔排气；

所述PLC控制器分别控制F塔的废气罐出气口阀门(f5)、C塔的产品气出气口阀门(c1)、C塔的进气口阀门(c4)、B塔的流通阀门(b2)、D塔的流通阀门(d2)、A塔的流通阀门(a3)、E塔的流通阀门(e3)在设定时间值  $\lambda$  后全部关闭；

步骤19、判断步骤18中阀门在设定时间值  $\lambda$  后是否全部关闭，若关闭，则执行步骤

20；否则，执行步骤34；

步骤20、六个吸附塔进行D塔充压循环工作：

所述PLC控制器分别控制A塔的流通阀门(a2)和F塔的流通阀门(f2)打开，用于给F塔冲洗；

所述PLC控制器分别控制B塔的流通阀门(b3)和E塔的流通阀门(e3)打开，用于给E塔充压；

所述PLC控制器控制C塔的进气口阀门(c4)打开，使得C塔进气，同时控制C塔的产品气出气口阀门(c1)打开，使得C塔出产品气；

所述PLC控制器控制D塔的产品气出气口阀门(d1)打开，使得D塔充压；

所述PLC控制器分别控制D塔的产品气出气口阀门(d1)、C塔的产品气出气口阀门(c1)、C塔的进气口阀门(c4)、B塔的流通阀门(b3)、E塔的流通阀门(e3)、A塔的流通阀门

(a2)、F塔的流通阀门(f2)在设定时间值  $\lambda$  后全部关闭；

步骤21、判断步骤20中阀门在设定时间值  $\lambda$  后是否全部关闭，若关闭，则执行步骤

22；否则，执行步骤34；

步骤22、六个吸附塔进行D塔吸附循环工作：

所述PLC控制器控制A塔的废气罐出气口阀门(a5)打开，用于给A塔排气；

所述PLC控制器分别控制B塔的流通阀门(b3)和F塔的流通阀门(f3)打开，用于给F塔充压；

所述PLC控制器分别控制C塔的流通阀门(c2)和E塔的流通阀门(e2)打开，用于给E塔充压；

所述PLC控制器控制D塔的进气口阀门(d4)打开，使得D塔进气，同时控制D塔的产品气出气口阀门(d1)打开，使得D塔出产品气；

所述PLC控制器分别控制D塔的产品气出气口阀门(d1)、D塔的进气口阀门(d4)、C塔的流通阀门(c2)、E塔的流通阀门(e2)、B塔的流通阀门(b3)、F塔的流通阀门(f3)、A塔的废气罐出气口阀门(a5)在设定时间值  $\lambda$  后全部关闭；

步骤23、判断步骤22中阀门在设定时间值  $\lambda$  后是否全部关闭，若关闭，则执行步骤

24；否则，执行步骤34；

步骤24、六个吸附塔进行E塔充压循环工作：

所述PLC控制器分别控制B塔的流通阀门(b2)和A塔的流通阀门(a2)打开，用于给A塔冲洗；

所述PLC控制器分别控制C塔的流通阀门(c3)和F塔的流通阀门(f3)打开，用于给F塔充压；

所述PLC控制器控制D塔的进气口阀门(d4)打开，使得D塔进气，同时控制D塔的产品气出气口阀门(d1)打开，使得D塔出产品气；

所述PLC控制器控制E塔的产品气出气口阀门(e1)打开，使得E塔进行充压；

所述PLC控制器分别控制E塔的产品气出气口阀门(e1)、D塔的产品气出气口阀门(d1)、D塔的进气口阀门(d4)、C塔的流通阀门(c3)、F塔的流通阀门(f3)、B塔的流通阀门

(b2)、A塔的流通阀门(a2)在设定时间值  $\lambda$  后全部关闭；

步骤25、判断步骤24中阀门在设定时间值  $\lambda$  后是否全部关闭，若关闭，则执行步骤

26；否则，执行步骤34；

步骤26、六个吸附塔进行E塔吸附循环工作：

所述PLC控制器分别控制C塔的流通阀门(c3)和A塔的流通阀门(a3)打开，用于给A塔充压；

所述PLC控制器控制B塔的废气罐出气口阀门(b5)打开，用于给B塔排气；

所述PLC控制器分别控制D塔的流通阀门(d2)和F塔的流通阀门(f2)打开，用于给F塔

充压；

所述PLC控制器控制E塔的进气口阀门(e4)打开，使得E塔进气，同时控制E塔的产品气出气口阀门(e1)打开，使得E塔出产品气；

所述PLC控制器分别控制E塔的产品气出气口阀门(e1)、E塔的进气口阀门(e4)、D塔的流通阀门(d2)、F塔的流通阀门(f2)、B塔的废气罐出气口阀门(b5)、C塔的流通阀门(c3)、A塔的流通阀门(a3)在设定时间值  $\lambda$  后全部关闭；

步骤27、判断步骤26中阀门在设定时间值  $\lambda$  后是否全部关闭，若关闭，则执行步骤

28；否则，执行步骤34；

步骤28、六个吸附塔进行F塔充压循环工作：

所述PLC控制器分别控制D塔的流通阀门(d2)和A塔的流通阀门(a2)打开，用于给A塔充压；

所述PLC控制器分别控制C塔的流通阀门(c3)和B塔的流通阀门(b3)打开，用于给B塔

冲洗；

所述PLC控制器控制E塔的进气口阀门(e4)打开，使得E塔进气，同时控制E塔的产品气出气口阀门(e1)打开，使得E塔出产品气；

所述PLC控制器控制F塔的产品气出气口阀门(f1)打开，使得F塔充压；

所述PLC控制器分别控制D塔的流通阀门(d2)、A塔的流通阀门(a2)、C塔的流通阀门(c3)、B塔的流通阀门(b3)、E塔的产品气出气口阀门(e1)、E塔的进气口阀门(e4)、F塔的产品气出气口阀门(f1)在设定时间值  $\lambda$  后全部关闭；

步骤29、判断步骤28中阀门在设定时间值  $\lambda$  后是否全部关闭，若关闭，则执行步骤

30；否则，执行步骤34；

步骤30、六个吸附塔进行F塔吸附循环工作：

所述PLC控制器分别控制E塔的流通阀门(e3)和A塔的流通阀门(a3)打开，用于给A塔充压；

所述PLC控制器分别控制D塔的流通阀门(d2)和B塔的流通阀门(b2)打开，用于给B塔充压；

所述PLC控制器控制C塔的废气罐出气口阀门(c5)打开，用于给C塔排气；

所述PLC控制器控制F塔的进气口阀门(f4)打开，使得F塔进气，同时控制F塔的产品

气出气口阀门(f1)打开，使得F塔出产品气；

所述PLC控制器分别控制F塔的产品气出气口阀门(f1)、F塔的进气口阀门(f4)、C塔的废气罐出气口阀门(c5)、D塔的流通阀门(d2)、B塔的流通阀门(b2)、E塔的流通阀门(e3)、A塔的流通阀门(a3)在设定时间值  $\lambda$  后全部关闭；

步骤31、判断步骤30中阀门在设定时间值  $\lambda$  后是否全部关闭，若关闭，则执行步骤

32；否则，执行步骤34；

步骤32、六个吸附塔进行A塔充压循环工作：

所述PLC控制器控制F塔的进气口阀门(f4)打开，使得F塔进气，同时控制F塔的产品气出气口阀门(f1)打开，使得F塔出产品气；

所述PLC控制器控制A塔的产品气出气口阀门(a1)打开，使得A塔充压；

所述PLC控制器分别控制E塔的流通阀门(e2)和B塔的流通阀门(b2)打开，用于给B塔充压；

所述PLC控制器分别控制D塔的流通阀门(d3)和C塔的流通阀门(c3)打开，用于给C塔冲洗；

所述PLC控制器分别控制F塔的产品气出气口阀门(f1)、F塔的进气口阀门(f4)、A塔的产品气出气口阀门(a1)、E塔的流通阀门(e2)、B塔的流通阀门(b2)、D塔的流通阀门(d3)、C

塔的流通阀门(c3)在设定时间值  $\lambda$  后全部关闭；

步骤33、判断步骤32中阀门在设定时间值  $\lambda$  后是否全部关闭，若关闭，则执行步骤

10；否则，执行步骤34；

步骤34、关闭所有阀门并停止控制，并将异常信号传输给所述报警模块进行报警；

步骤35、判断所述产品罐(4)内气体产量是否低于目标产量L；若低于，则执行步骤

10；若否则，表示完成低浓度瓦斯的提浓，并停止工作。

# 说明书

# 一种基于PLC的低浓度煤层气抑爆式提浓控制方法

# 技术领域

本发明属于工业生产控制领域，具体的说是一种基于PLC的低浓度煤层气抑爆式提浓控制方法。

# 背景技术

煤层气是以甲烷（ $\mathrm{CH}_4$ ）为主要成分的气体，由于抽采时低浓度（ $\leq 20 - 30\%$ ）的煤层气会被排空，这不仅造成资源的浪费，而且会加剧温室效应。从目前我国煤层气利用发展趋势来看，低浓度煤层气开发利用的潜力巨大，当下在低浓度煤层气提浓领域取得较多研究成果的是变压吸附技术、真空吸附技术和膜分离技术，其中吸附法的缺点有：在使用的过程中

需要预处理废气中的粉尘、烟等杂质；高温废气需要冷却。投资费用较大膜分离技术缺点有：膜面易发生污染，致使膜分离性能降低，故需采用与工艺相适应的膜面清洗方法；稳定性、耐药性、耐热性、耐溶剂能力有限，故使用范围有限；单独的膜分离技术功能有限，需与其他分离技术连用。

变压吸附在实际应用之中还有一些不足之处。一是“解吸”阶段，在对一个吸附罐进行解吸时，一般需要通过另一个吸附罐制出的一部分瓦斯进行辅助解吸，容易出现回流现象，导致影响产品气的制备；二是对于制造出的瓦斯，因为压力问题需要设置缓冲罐，同时对于制造的瓦斯需要进行温度控制，现有的设备需要较多的装置很难实现缓冲以及控温效果。

# 发明内容

本发明是为了解决上述现有技术存在的不足之处，提出一种基于PLC的低浓度煤层气抑爆式提浓控制方法，以前能实时监测煤层气浓度，并对提浓过程中的控检测数据和调节参数进行实时优化与控制，在出现爆炸情况时及时报警，从而确保整个工艺流程处于安全、高效运行状态。

本发明为达到上述发明目的，采用如下技术方案：

（省略复制）

与现有技术相比，本发明的有益效果在于：

本发明通过PLC与变压吸附设备的信号传输，来实现远程监控、调节变压吸附中阀门开关和气体浓度，从而判断设备能否正常运行并做出相应的调整，实现了远程控制，且不需要人员在现场操作，解决了复杂工艺顺畅运行的同时进一步保障了现场的安全。

# 附图说明

图1为本发明的抑爆式提浓控制方法的结构示意图；

图2为本发明抑爆式提浓控制方法六塔的变压吸附流程图；

图中标号：1为原料罐；2为缓冲罐；3为废气罐；4为产品罐；5为压缩机；001为总阀门；a1为A塔的产品气出气口阀门；a2为A塔的流通阀门；a3为A塔的流通阀门；a4为A塔的进气口阀门；a5为A塔的废气罐出气口阀门；b1为B塔的产品气出气口阀门；b2为B塔的流通阀门；b3为B塔的流通阀门；b4为B塔的进气口阀门；b5为B塔的废气罐出气口阀门；c1为C塔的产品气出气口阀门；c2为C塔的流通阀门；c3为C塔的流通阀门；c4为C塔的进气口阀门；c5为C塔的废气罐出气口阀门；d1为D塔的产品气出气口阀门；d2为D塔的流通阀门；d3为D塔的流通阀门；d4为D塔的进气口阀门；d5为D塔的废气罐出气口阀门；e1为E塔的产品气出气口阀门；e2为E塔的流通阀门；e3为E塔的流通阀门；e4为E塔的进气口阀门；e5为E塔的废气罐出气口阀门；f1为F塔的产品气出气口阀门；f2

为 F 塔的流通阀门；f3 为 F 塔的流通阀门；f4 为 F 塔的进气口阀门；f5 为 F 塔的废气罐出气口阀门。

# 具体实施方式

以下结合附图和实施例对本发明作进一步说明。

本实施例中，一种基于PLC的低浓度煤层气抑爆式提浓控制方法是应用于低浓度煤层气抑爆式提浓的控制系统中，控制系统包括：PLC控制器、压缩机5、总阀门001、若干个阀门、变压吸附系统、若干个继电器、报警模块、压力变送器、温度变送器、浓度变送器；

变压吸附系统包括：原料罐1、缓冲罐2、六个吸附塔、废气罐3、产品罐4；

原料罐1和压缩机5之间设置有总阀门001并通过导气管连接；压缩机5和缓冲罐4之间通过导气管连接；具体连接图如图1所示；

六个吸附塔是由A塔、B塔、C塔、D塔、E塔和F塔组成；

六个吸附塔分别通过六个进气管与缓冲罐2连通，并在每个进气管上设置有对应的进气口阀门；

六个吸附塔分别通过六个废气管与废气罐3连通，并在每个废气管上设置有对应的废气罐出气口阀门；

六个吸附塔分别通过六个出气管与产品罐4连通，并在每个出气管上设置有对应的产品

气出气口阀门；

六个吸附塔之间分别通过一个流通管互相连通，并在六个吸附塔所对应的流通管上设置有并列的2个流通阀门；流通阀门的作用是让两个塔在变压吸附的过程中，能够联通两个塔，使两个塔之间实现充压和冲洗；

压力变送器分别放置在缓冲罐2和六个吸附塔中；

浓度变送器分别放置在缓冲罐2、废气罐3和产品罐4中；工作过程中，通过模拟信号接收器将原料气的压强通过压力变送器和浓度变送器把数值传到PLC系统中，通过对数据的处理来得到吸附塔内压强和浓度；

温度变送器分别放置在六个吸附塔的外侧；能够检测设备的运行时的温度，防止系统温度过高引起的危险；

本实施例中，低浓度煤层气抑爆式提浓控制方法是按如下步骤进行：

步骤1、PLC控制器发送开车信号给变压吸附系统，变压吸附系统根据开车信号打开总阀门001，使得压缩机5工作并开始对原料罐1中的气体进行升压，从而将升压后的气体送入缓冲罐2；

步骤2、PLC控制器利用压强传感器采集缓冲罐2的压力值，并判断压力值是否达到指定范围；若达到，则执行步骤3；否则，执行步骤9；

步骤3、PLC控制器控制压缩机5的当前功率保持不变，并打开A塔的进气口阀门a4用于对A塔进行充气；

步骤4、记录A塔的进气管上的进气口阀门a4开启时间点  $\mathrm{T_{A0}}$  以及A塔的进气时间  $\mathrm{T_A}$  从而得到气体到达A塔所需的时间  $\mathrm{t_A = T_A - T_{A0}}$  ，并计算A塔对吸附质的吸附时间  $\mathrm{t_{A0} = t_A}$ $+\mathrm{t}) - (\frac{1 - \epsilon}{\nu_{As}})\mathrm{z}$  ；其中，表示吸附剂颗粒之间的轴向距离，表示A塔中吸附剂的地层孔隙度，  $\nu_{\mathrm{As}}$  表示A塔中气体流动速率，  $\mathrm{t_A}$  示气体到达A塔的时间，进气时间  $\mathrm{T_A}$  是指利用压强传感器检测A塔的压强信号PA的时间点；

步骤5、PLC控制器判断A塔的压强信号PA是否达到最佳吸附压强；若达到，则执行步骤6；否则，增加压缩机5的当前功率并继续对A塔进行充气；执行步骤4；

步骤6、PLC控制器判断A塔的吸附时间  $\mathrm{t_{A0}}$  是否达到设定时间  $\mathrm{t_{max}}$  ，若达到，则执行步骤7；否则，判断在吸附时间  $\mathrm{t_{A0}}$  内，压强信号PA的变化波动范围是否超过阈值，若是，则表示A塔的出气管上的产品气出气口阀门a1或者A塔的气密性不足，并执行步骤34；否则，则执行步骤7；

步骤7、PLC控制器控制继电器打开废气罐3出气口阀门a5，并利用废气罐3的浓度变送器对A塔排出气体进行检测，执行步骤8；

步骤8、判断废气罐3A塔排出气体浓度是否在设定的吸附塔浓度变化范围内；若是，

则关闭废气罐出气口阀门a5并执行步骤10；否则，关闭阀门废气罐3出气口阀门a5，并将吸附时间  $\mathfrak{t}_{\mathrm{A0}}$  加上  $\delta_{\times \mathrm{t_{A0}}}$  后，返回步骤6顺序执行；  $\delta$  表示0—1之间的倍数；

步骤9、判断在压缩机5开始工作时间  $\Delta t$  内缓冲罐2内的压强是否达到设定值，若达到，则执行步骤3；否则，将调大压缩机5的工作功率，执行步骤3；

步骤10、六个吸附塔进行A塔吸附循环工作：

PLC控制器控制A塔的进气口阀门a4打开，使得A塔进气，同时控制A塔的产品气出气口阀门a1打开，使得A塔出产品气；

PLC控制器分别控制F塔的流通阀门f3和B塔的流通阀门b3打开，用于给B塔充压；

PLC控制器分别控制E塔的流通阀门e2和C塔的流通阀门c2打开，用于给C塔充压；

PLC控制器控制D塔的废气罐出气口阀门d5打开，使得D塔排废气；

PLC控制器分别控制D塔的废气罐出气口阀门d5、E塔的流通阀门e2、C塔的流通阀

门c2、F塔的流通阀门f3、B塔的流通阀门b3、A塔的产品气出气口阀门a1、A塔的进气

口阀门a4在设定时间值后全部关闭；

步骤11、判断步骤10中阀门在设定时间值后是否全部关闭，若关闭，则执行步骤

12；否则，执行步骤34；

步骤12、六个吸附塔进行B塔充压循环工作：

PLC控制器控制A塔的进气口阀门a4打开，使得A塔进气，同时控制A塔的产品气出气口阀门a1打开，使得A塔出产品气；

PLC控制器控制B塔的产品气出气口阀门b1打开，用于对B塔充压；

PLC控制器分别控制F塔的流通阀门f3和C塔的流通阀门c3打开，用于给C塔充压；

PLC控制器分别控制E塔的流通阀门e2和D塔的流通阀门d2打开，用于给D塔冲洗；

PLC控制器分别控制E塔的流通阀门e2、D塔的流通阀门d2、F塔的流通阀门f3、C

塔的流通阀门c3、B塔的产品气出气口阀门b1、A塔的产品气出气口阀门a1、A塔的进气口阀门a4在设定时间值后全部关闭；

步骤13、判断步骤12中阀门在设定时间值后是否全部关闭，若关闭，则执行步骤

14；否则，执行步骤34；

步骤14、六个吸附塔进行B塔吸附循环工作：

PLC控制器分别控制A塔的流通阀门a2和C塔的流通阀门c2打开，用于给C塔充压；

PLC控制器控制B塔的进气口阀门b4打开，使得B塔进气，同时控制B塔的产品气出气口阀门b1打开，使得B塔出产品气；

PLC控制器分别控制F塔的流通阀门f3和D塔的流通阀门d3打开，用于给D塔充压；

PLC控制器控制E塔的废气罐出气口阀门e5打开，使得E塔排废气；

PLC 控制器分别控制 E 塔的废气罐出气口阀门 e5、F 塔的流通阀门 f3、D 塔的流通阀门 d3、B 塔的产品气出气口阀门 b1、B 塔的进气口阀门 b4、A 塔的流通阀门 a2、C 塔的流通阀门 c2 在设定时间值  $\lambda$  后全部关闭；

步骤 15、判断步骤 14 中阀门在设定时间值  $\lambda$  后是否全部关闭，若关闭，则执行步骤

16；否则，执行步骤 34；

步骤 16、六个吸附塔进行 C 塔充压循环工作：

PLC 控制器分别控制 A 塔的流通阀门 a3 和 D 塔的流通阀门 d3 打开，用于给 D 塔充压；

PLC 控制器控制 B 塔的进气口阀门 b4 打开，使得 B 塔进气，同时控制 B 塔的产品气出气口阀门 b1 打开，使得 B 塔出产品气；

PLC 控制器控制 C 塔的产品气出气口阀门 c1 打开，用于给 C 塔充压；

PLC 控制器分别控制 F 塔的流通阀门 f2 和 E 塔的流通阀门 e2 打开，用于给 E 塔冲洗；

PLC 控制器分别控制 F 塔的流通阀门 f2、E 塔的流通阀门 e2、C 塔的产品气出气口阀门

c1、B 塔的产品气出气口阀门 b1、B 塔的进气口阀门 b4、A 塔的流通阀门 a3、D 塔的流通阀门 d3 在设定时间值  $\lambda$  后全部关闭；

步骤 17、判断步骤 16 中阀门在设定时间值  $\lambda$  后是否全部关闭，若关闭，则执行步骤

18；否则，执行步骤 34；

步骤18、六个吸附塔进行C塔吸附循环工作：

PLC控制器分别控制A塔的流通阀门a3和E塔的流通阀门e3打开，用于给E塔充压；

PLC控制器分别控制B塔的流通阀门b2和D塔的流通阀门d2打开，用于给D塔充压；

PLC控制器控制C塔的进气口阀门c4打开，使得C塔进气，同时控制C塔的产品气出气口阀门c1打开，使得C塔出产品气；

PLC控制器控制F塔的废气罐出气口阀门f5打开，用于F塔排气；

PLC控制器分别控制F塔的废气罐出气口阀门f5、C塔的产品气出气口阀门c1、C塔的进气口阀门c4、B塔的流通阀门b2、D塔的流通阀门d2、A塔的流通阀门a3、E塔的流通阀门e3在设定时间值  $\lambda$  后全部关闭；

步骤19、判断步骤18中阀门在设定时间值  $\lambda$  后是否全部关闭，若关闭，则执行步骤

20；否则，执行步骤34；

步骤20、六个吸附塔进行D塔充压循环工作：

PLC控制器分别控制A塔的流通阀门a2和F塔的流通阀门f2打开，用于给F塔冲洗；

PLC控制器分别控制B塔的流通阀门b3和E塔的流通阀门e3打开，用于给E塔充压；

PLC控制器控制C塔的进气口阀门c4打开，使得C塔进气，同时控制C塔的产品气出

气口阀门c1打开，使得C塔出产品气；

PLC控制器控制D塔的产品气出气口阀门d1打开，使得D塔充压；

PLC控制器分别控制D塔的产品气出气口阀门d1、C塔的产品气出气口阀门c1、C塔的进气口阀门c4、B塔的流通阀门b3、E塔的流通阀门e3、A塔的流通阀门a2、F塔的流通阀门f2在设定时间值后全部关闭；

步骤21、判断步骤20中阀门在设定时间值后是否全部关闭，若关闭，则执行步骤

22；否则，执行步骤34；

步骤22、六个吸附塔进行D塔吸附循环工作：

PLC控制器控制A塔的废气罐出气口阀门a5打开，用于给A塔排气；

PLC控制器分别控制B塔的流通阀门b3和F塔的流通阀门f3打开，用于给F塔充压；

PLC控制器分别控制C塔的流通阀门c2和E塔的流通阀门e2打开，用于给E塔充压；

PLC控制器控制D塔的进气口阀门d4打开，使得D塔进气，同时控制D塔的产品气出

气口阀门d1打开，使得D塔出产品气；

PLC控制器分别控制D塔的产品气出气口阀门d1、D塔的进气口阀门d4、C塔的流通阀门c2、E塔的流通阀门e2、B塔的流通阀门b3、F塔的流通阀门f3、A塔的废气罐出气口阀门a5在设定时间值后全部关闭；

步骤23、判断步骤22中阀门在设定时间值后是否全部关闭，若关闭，则执行步骤

24；否则，执行步骤34；

步骤24、六个吸附塔进行E塔充压循环工作：

PLC控制器分别控制B塔的流通阀门b2和A塔的流通阀门a2打开，用于给A塔冲洗；

PLC控制器分别控制C塔的流通阀门c3和F塔的流通阀门b3打开，用于给F塔充压；

PLC控制器控制D塔的进气口阀门d4打开，使得D塔进气，同时控制D塔的产品气出

气口阀门d1打开，使得D塔出产品气；

PLC控制器控制E塔的产品气出气口阀门e1打开，使得E塔进行充压；

PLC控制器分别控制E塔的产品气出气口阀门e1、D塔的产品气出气口阀门d1、D塔的进气口阀门d4、C塔的流通阀门c3、F塔的流通阀门f3、B塔的流通阀门b2、A塔的流通阀门a2在设定时间值后全部关闭；

步骤25、判断步骤24中阀门在设定时间值后是否全部关闭，若关闭，则执行步骤

26；否则，执行步骤34；

步骤26、六个吸附塔进行E塔吸附循环工作：

PLC控制器分别控制C塔的流通阀门c3和A塔的流通阀门a3打开，用于给A塔充压；

PLC控制器控制B塔的废气罐出气口阀门b5打开，用于给B塔排气；

PLC控制器分别控制D塔的流通阀门d2和F塔的流通阀门f2打开，用于给F塔充压；

PLC控制器控制E塔的进气口阀门e4打开，使得E塔进气，同时控制E塔的产品气出

气口阀门e1打开，使得E塔出产品气；

PLC控制器分别控制E塔的产品气出气口阀门e1、E塔的进气口阀门e4、D塔的流通阀门d2、F塔的流通阀门f2、B塔的废气罐出气口阀门b5、C塔的流通阀门c3、A塔的流通阀门a3在设定时间值后全部关闭；

步骤27、判断步骤26中阀门在设定时间值后是否全部关闭，若关闭，则执行步骤

28；否则，执行步骤34；

步骤28、六个吸附塔进行F塔充压循环工作：

PLC控制器分别控制D塔的流通阀门d2和A塔的流通阀门a2打开，用于给A塔充压；

PLC控制器分别控制C塔的流通阀门c3和B塔的流通阀门b3打开，用于给B塔冲洗；

PLC控制器控制E塔的进气口阀门e4打开，使得E塔进气，同时控制E塔的产品气出

气口阀门e1打开，使得E塔出产品气；

PLC控制器控制F塔的产品气出气口阀门f1打开，使得F塔充压；

PLC控制器分别控制D塔的流通阀门d2、A塔的流通阀门a2、C塔的流通阀门c3、B塔的流通阀门b3、E塔的产品气出气口阀门e1、E塔的进气口阀门e4、F塔的产品气出气口阀门f1在设定时间值后全部关闭；

步骤29、判断步骤28中阀门在设定时间值后是否全部关闭，若关闭，则执行步骤

30；否则，执行步骤34；

步骤30、六个吸附塔进行F塔吸附循环工作：

PLC控制器分别控制E塔的流通阀门e3和A塔的流通阀门a3打开，用于给A塔充压；

PLC控制器分别控制D塔的流通阀门d2和B塔的流通阀门b2打开，用于给B塔充压；

PLC控制器控制C塔的废气罐出气口阀门c5打开，用于给C塔排气；

PLC控制器控制F塔的进气口阀门f4打开，使得F塔进气，同时控制F塔的产品气出气口阀门f1打开，使得F塔出产品气；

PLC控制器分别控制F塔的产品气出气口阀门f1、F塔的进气口阀门f4、C塔的废气罐出气口阀门c5、D塔的流通阀门d2、B塔的流通阀门b2、E塔的流通阀门e3、A塔的流通阀门a3在设定时间值后全部关闭；

步骤31、判断步骤30中阀门在设定时间值后是否全部关闭，若关闭，则执行步骤

32；否则，执行步骤34；

步骤32、六个吸附塔进行A塔充压循环工作：

PLC控制器控制F塔的进气口阀门f4打开，使得F塔进气，同时控制F塔的产品气出气口阀门f1打开，使得F塔出产品气；

PLC控制器控制A塔的产品气出气口阀门a1打开，使得A塔充压；

PLC控制器分别控制E塔的流通阀门e2和B塔的流通阀门b2打开，用于给B塔充压；PLC控制器分别控制D塔的流通阀门d3和C塔的流通阀门c3打开，用于给C塔冲洗；PLC控制器分别控制F塔的产品气出气口阀门f1、F塔的进气口阀门f4、A塔的产品气出气口阀门a1、E塔的流通阀门e2、B塔的流通阀门b2、D塔的流通阀门d3、C塔的流通阀门c3在设定时间值  $\lambda$  后全部关闭；

步骤33、判断步骤32中阀门在设定时间值  $\lambda$  后是否全部关闭，若关闭，则执行步骤

10；否则，执行步骤34；

步骤34、关闭所有阀门并停止控制，并将异常信号传输给报警模块进行报警；此步骤是将变压吸附塔的出气阀门全部关闭，经过缓冲罐2的原料气入口阀门关闭同时压缩机5停止工作，能有效防止原料气压强过高和高浓度的产品气泄露。

步骤35、判断产品罐4内气体产量是否低于目标产量L；若低于，则执行步骤10；若否则，表示完成低浓度瓦斯的提浓，并停止工作。

![](images/974c8deff96a426b169eacfcbd38f33c10aad497f8c16c2a65772653fc7483cc.jpg)  
图1

![](images/748e0a3793ebee6a8fca01cb3fc671b42f977016926f67ecad2a0bc0a31ed799.jpg)  
图2